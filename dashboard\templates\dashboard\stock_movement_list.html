{% extends "dashboard/base.html" %}
{% block title %}Pergerakan Stok{% endblock %}

{% block head_extras %}
<style>
    /* Styling untuk halaman pergerakan stok */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
    }
    
    .action-buttons {
        display: flex;
        gap: 10px;
    }
    
    /* Filter box styling */
    .filter-box {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .filter-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        font-size: 14px;
        color: #555;
    }
    
    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.2s, box-shadow 0.2s;
    }
    
    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
        outline: none;
    }
    
    .form-select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23555' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 12px center;
        transition: border-color 0.2s, box-shadow 0.2s;
    }
    
    .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
        outline: none;
    }
    
    .input-icon-wrapper {
        position: relative;
    }
    
    .input-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }
    
    .input-with-icon {
        padding-left: 35px;
    }
    
    .filter-buttons {
        display: flex;
        gap: 10px;
        margin-top: 10px;
    }
    
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: background-color 0.2s, transform 0.1s;
        border: none;
    }
    
    .btn:active {
        transform: translateY(1px);
    }
    
    .btn i {
        margin-right: 6px;
    }
    
    .btn-primary {
        background-color: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background-color: #2980b9;
    }
    
    .btn-secondary {
        background-color: #95a5a6;
        color: white;
    }
    
    .btn-secondary:hover {
        background-color: #7f8c8d;
    }
    
    .btn-success {
        background-color: #2ecc71;
        color: white;
    }
    
    .btn-success:hover {
        background-color: #27ae60;
    }
    
    .btn-danger {
        background-color: #e74c3c;
        color: white;
    }
    
    .btn-danger:hover {
        background-color: #c0392b;
    }
    
    /* Table styling */
    .table-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .data-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .data-table th {
        background-color: #f8f9fa;
        padding: 12px 15px;
        text-align: left;
        font-weight: 600;
        font-size: 13px;
        color: #555;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .data-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 14px;
        vertical-align: middle;
    }
    
    .data-table tr:last-child td {
        border-bottom: none;
    }
    
    .data-table tr:hover td {
        background-color: #f9f9f9;
    }
    
    .badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .bg-success {
        background-color: #d4edda;
        color: #155724;
    }
    
    .bg-danger {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    /* Empty state */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
    }
    
    .empty-state i {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 15px;
    }
    
    .empty-state p {
        color: #777;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="page-header">
        <h1 class="page-title">Pergerakan Stok</h1>
        <div class="action-buttons">
            <a href="{% url 'dashboard:stock_in' %}" class="btn btn-success">
                <i class="fas fa-plus"></i> Stok Masuk
            </a>
            <a href="{% url 'dashboard:stock_out' %}" class="btn btn-danger">
                <i class="fas fa-minus"></i> Stok Keluar
            </a>
        </div>
    </div>
    
    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Form Pencarian dan Filter -->
    <div class="filter-box">
        <form method="get" class="filter-form">
            <div class="form-group">
                <label for="{{ form.search.id_for_label }}">Cari Produk</label>
                <div class="input-icon-wrapper">
                    <i class="fas fa-search input-icon"></i>
                    <input type="text" name="{{ form.search.name }}" id="{{ form.search.id_for_label }}" 
                           value="{{ form.search.value|default:'' }}" class="form-control input-with-icon" 
                           placeholder="Cari produk">
                </div>
            </div>
            <div class="form-group">
                <label for="{{ form.movement_type.id_for_label }}">Tipe Pergerakan</label>
                <div class="input-icon-wrapper">
                    <i class="fas fa-exchange-alt input-icon"></i>
                    <select name="{{ form.movement_type.name }}" id="{{ form.movement_type.id_for_label }}" 
                            class="form-select input-with-icon">
                        <option value="">Semua Tipe</option>
                        <option value="IN" {% if form.movement_type.value == 'IN' %}selected{% endif %}>Stok Masuk</option>
                        <option value="OUT" {% if form.movement_type.value == 'OUT' %}selected{% endif %}>Stok Keluar</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="{{ form.date_from.id_for_label }}">Dari Tanggal</label>
                <div class="input-icon-wrapper">
                    <i class="fas fa-calendar-alt input-icon"></i>
                    <input type="date" name="{{ form.date_from.name }}" id="{{ form.date_from.id_for_label }}" 
                           value="{{ form.date_from.value|default:'' }}" class="form-control input-with-icon">
                </div>
            </div>
            <div class="form-group">
                <label for="{{ form.date_to.id_for_label }}">Sampai Tanggal</label>
                <div class="input-icon-wrapper">
                    <i class="fas fa-calendar-alt input-icon"></i>
                    <input type="date" name="{{ form.date_to.name }}" id="{{ form.date_to.id_for_label }}" 
                           value="{{ form.date_to.value|default:'' }}" class="form-control input-with-icon">
                </div>
            </div>
            <div class="filter-buttons">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Cari
                </button>
                <a href="{% url 'dashboard:stock_movement_list' %}" class="btn btn-secondary">
                    <i class="fas fa-sync"></i> Reset
                </a>
            </div>
        </form>
    </div>
    
    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Tanggal</th>
                        <th>Produk</th>
                        <th>Tipe</th>
                        <th>Jumlah</th>
                        <th>Catatan</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movement in movements %}
                    <tr>
                        <td>{{ movement.timestamp|date:"d/m/Y H:i" }}</td>
                        <td>
                            <a href="{% url 'dashboard:product_detail' movement.product.id %}" class="text-primary">
                                {{ movement.product.name }}
                            </a>
                        </td>
                        <td>
                            {% if movement.movement_type == 'IN' %}
                            <span class="badge badge-success">
                                <i class="fas fa-arrow-circle-down"></i> Masuk
                            </span>
                            {% else %}
                            <span class="badge badge-danger">
                                <i class="fas fa-arrow-circle-up"></i> Keluar
                            </span>
                            {% endif %}
                        </td>
                        <td>{{ movement.quantity }}</td>
                        <td>{{ movement.notes|default:"-" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5">
                            <div class="empty-state">
                                <i class="fas fa-exchange-alt"></i>
                                <p>Tidak ada transaksi stok yang ditemukan</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}


