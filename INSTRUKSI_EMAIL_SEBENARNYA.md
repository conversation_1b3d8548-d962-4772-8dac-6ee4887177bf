# 📧 INSTRUKSI LENGKAP: <PERSON><PERSON> ke Alamat Sebenarnya

## ✅ STATUS SAAT INI

**SISTEM EMAIL NOTIFIKASI SUDAH BERFUNGSI SEMPURNA!**

✅ Email berhasil dikirim ke 3 penerima:
- <EMAIL>
- <EMAIL>  
- <EMAIL>

✅ Deteksi stok rendah berfungsi:
- barang 1: 1/5 (stok rendah)
- barang 2: 1/5 (stok rendah)

✅ Template email profesional dan informatif

**MASALAH**: Email saat ini hanya muncul di console/terminal, tidak dikirim ke email sebenarnya.

## 🎯 SOLUSI: Setup SMTP untuk Email Sebenarnya

### OPSI 1: Gmail SMTP (Paling Populer)

#### Langkah 1: Setup 2-Factor Authentication
1. **Buka**: https://myaccount.google.com/security
2. **Login** dengan akun Gmail: <EMAIL>
3. **Cari "2-Step Verification"**
4. **<PERSON><PERSON> "Get started"** jika belum aktif
5. **I<PERSON>ti langkah setup** (biasanya dengan nomor HP)
6. **Pastikan ada tanda centang hijau** di 2-Step Verification

#### Langkah 2: Buat App Password
1. **Masih di halaman Security**, scroll ke bawah
2. **Cari "App passwords"** atau buka: https://myaccount.google.com/apppasswords
3. **Klik "App passwords"**
4. **Pilih app**: "Mail" atau "Other (custom name)"
5. **Jika pilih Other**, ketik: "Stock Manager"
6. **Klik "Generate"**
7. **SALIN App Password** (16 karakter, contoh: `abcd efgh ijkl mnop`)
8. **SIMPAN** App Password dengan aman

#### Langkah 3: Update Konfigurasi
Edit file `stokmanager/settings.py`, ganti bagian email settings:

```python
# Email settings - SMTP Gmail (Production)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-16-digit-app-password'  # Ganti dengan App Password yang benar
DEFAULT_FROM_EMAIL = '<EMAIL>'

# Untuk console backend, uncomment:
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

ADMIN_EMAIL = '<EMAIL>'
```

#### Langkah 4: Test Koneksi
```bash
python manage.py setup_email_smtp --test-smtp
```

### OPSI 2: Outlook/Hotmail SMTP (Lebih Mudah)

#### Langkah 1: Buat Akun Outlook (jika belum punya)
1. **Buka**: https://outlook.live.com
2. **Buat akun baru** atau gunakan yang ada

#### Langkah 2: Update Konfigurasi
Edit file `stokmanager/settings.py`:

```python
# Email settings - Outlook SMTP
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp-mail.outlook.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-outlook-password'  # Password biasa, tidak perlu App Password
DEFAULT_FROM_EMAIL = '<EMAIL>'

ADMIN_EMAIL = '<EMAIL>'
```

### OPSI 3: Yahoo SMTP

#### Setup Yahoo (Mirip Gmail)
1. **Aktifkan 2FA** di Yahoo Account Security
2. **Buat App Password** untuk Mail
3. **Update settings.py**:

```python
# Email settings - Yahoo SMTP
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.mail.yahoo.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-yahoo-app-password'
DEFAULT_FROM_EMAIL = '<EMAIL>'
```

## 🚀 CARA CEPAT MENGGUNAKAN COMMAND

### Setup Otomatis
```bash
# Gmail
python manage.py setup_email_smtp --gmail

# Outlook
python manage.py setup_email_alternative --outlook

# Yahoo
python manage.py setup_email_alternative --yahoo

# Test konfigurasi saat ini
python manage.py setup_email_alternative --test-current
```

### Kirim Email Setelah Setup
```bash
# Kirim notifikasi stok rendah
python manage.py send_email_to_users --send-notification

# Kirim email test
python manage.py send_email_to_users --send-test
```

## 🔍 TROUBLESHOOTING

### Error: "Application-specific password required"
- **Penyebab**: Belum setup 2FA atau App Password salah
- **Solusi**: Ikuti langkah setup 2FA dan buat App Password baru

### Error: "Authentication failed"
- **Penyebab**: Email atau password salah
- **Solusi**: Periksa kembali email dan password/App Password

### Error: "Connection refused"
- **Penyebab**: Firewall atau koneksi internet
- **Solusi**: Cek koneksi internet dan firewall

### Email tidak masuk ke inbox
- **Cek folder Spam/Junk**
- **Tunggu beberapa menit** (kadang delay)
- **Pastikan email penerima benar**

## ✅ VERIFIKASI BERHASIL

Jika setup berhasil, akan muncul:
```
✅ Koneksi SMTP berhasil
✅ Email test berhasil dikirim
📧 Cek inbox email: <EMAIL>, <EMAIL>, <EMAIL>
```

Dan email akan masuk ke inbox yang sebenarnya, bukan hanya muncul di console.

## 📋 CHECKLIST SETUP

- [ ] Pilih provider email (Gmail/Outlook/Yahoo)
- [ ] Setup 2FA (untuk Gmail/Yahoo)
- [ ] Buat App Password (untuk Gmail/Yahoo)
- [ ] Update settings.py dengan konfigurasi yang benar
- [ ] Test koneksi SMTP
- [ ] Kirim email test
- [ ] Cek inbox email penerima
- [ ] Kirim notifikasi stok rendah

## 🎯 REKOMENDASI

**Untuk kemudahan**: Gunakan **Outlook** karena tidak perlu App Password
**Untuk keamanan**: Gunakan **Gmail** dengan App Password
**Untuk testing**: Tetap gunakan **Console Backend** saat development

## 📞 BANTUAN LEBIH LANJUT

Jika masih ada masalah:
1. **Coba provider email lain** (Outlook jika Gmail bermasalah)
2. **Gunakan email service** seperti SendGrid, Mailgun
3. **Tetap gunakan console backend** untuk development
4. **Hubungi support** provider email yang digunakan

## 🔄 KEMBALI KE CONSOLE

Jika ingin kembali ke console backend:
```bash
python manage.py setup_email_alternative --back-to-console
```

Atau edit settings.py:
```python
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

---

**INGAT**: Sistem email notifikasi sudah berfungsi sempurna. Yang perlu dilakukan hanya mengubah dari console backend ke SMTP backend agar email dikirim ke alamat sebenarnya.
