from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.conf import settings
from dashboard.utils import get_admin_and_owner_emails, send_low_stock_email_notification
from dashboard.models import Product
from django.db.models import F

class Command(BaseCommand):
    help = 'Kirim email notifikasi stok rendah langsung ke email yang terdaftar di manajemen pengguna'

    def add_arguments(self, parser):
        parser.add_argument(
            '--list-users',
            action='store_true',
            help='Tampilkan daftar user dan email mereka',
        )
        parser.add_argument(
            '--send-notification',
            action='store_true',
            help='Kirim notifikasi stok rendah ke semua admin/owner',
        )
        parser.add_argument(
            '--send-test',
            action='store_true',
            help='Kirim email test ke semua admin/owner',
        )

    def handle(self, *args, **options):
        if options['list_users']:
            self.list_users()
        elif options['send_notification']:
            self.send_stock_notification()
        elif options['send_test']:
            self.send_test_email()
        else:
            self.show_help()

    def show_help(self):
        self.stdout.write(self.style.SUCCESS('=== KIRIM EMAIL KE USER TERDAFTAR ===\n'))
        self.stdout.write('Gunakan salah satu opsi berikut:')
        self.stdout.write('  --list-users        : Lihat daftar user dan email')
        self.stdout.write('  --send-notification : Kirim notifikasi stok rendah')
        self.stdout.write('  --send-test         : Kirim email test')
        self.stdout.write('\nContoh:')
        self.stdout.write('  python manage.py send_email_to_users --list-users')
        self.stdout.write('  python manage.py send_email_to_users --send-notification')

    def list_users(self):
        self.stdout.write(self.style.SUCCESS('=== DAFTAR USER DAN EMAIL ===\n'))
        
        # Tampilkan semua user
        all_users = User.objects.all().order_by('username')
        self.stdout.write(f'Total user: {all_users.count()}\n')
        
        for user in all_users:
            # Tentukan role
            if user.is_superuser:
                role = '👑 Owner'
                role_color = self.style.ERROR
            elif user.is_staff:
                role = '🔧 Admin'
                role_color = self.style.WARNING
            else:
                role = '👤 User'
                role_color = self.style.HTTP_INFO
            
            # Status email
            email_status = user.email if user.email else '❌ Tidak ada email'
            active_status = '✅ Aktif' if user.is_active else '❌ Tidak aktif'
            
            self.stdout.write(f'{role_color(role)} {user.username}')
            self.stdout.write(f'   📧 Email: {email_status}')
            self.stdout.write(f'   🔄 Status: {active_status}')
            self.stdout.write('')
        
        # Tampilkan email yang akan menerima notifikasi
        recipient_emails = get_admin_and_owner_emails()
        self.stdout.write(self.style.SUCCESS(f'📧 EMAIL PENERIMA NOTIFIKASI ({len(recipient_emails)}):'))
        if recipient_emails:
            for email in recipient_emails:
                self.stdout.write(f'   ✅ {email}')
        else:
            self.stdout.write(self.style.ERROR('   ❌ TIDAK ADA EMAIL PENERIMA YANG VALID!'))
            self.stdout.write('   💡 Pastikan admin/owner memiliki email dan akun aktif')

    def send_stock_notification(self):
        self.stdout.write(self.style.SUCCESS('=== KIRIM NOTIFIKASI STOK RENDAH ===\n'))
        
        # Cek email penerima
        recipient_emails = get_admin_and_owner_emails()
        if not recipient_emails:
            self.stdout.write(self.style.ERROR('❌ Tidak ada email penerima yang valid'))
            self.stdout.write('💡 Pastikan admin/owner memiliki email dan akun aktif')
            return
        
        self.stdout.write(f'📧 Email penerima: {", ".join(recipient_emails)}')
        
        # Cek produk stok rendah/habis
        low_stock_products = Product.objects.filter(quantity__lte=F('min_stock_level'), quantity__gt=0)
        out_of_stock_products = Product.objects.filter(quantity=0)
        
        self.stdout.write(f'📦 Produk stok rendah: {low_stock_products.count()}')
        self.stdout.write(f'📦 Produk stok habis: {out_of_stock_products.count()}')
        
        if not low_stock_products.exists() and not out_of_stock_products.exists():
            self.stdout.write(self.style.SUCCESS('✅ Tidak ada produk yang memerlukan notifikasi'))
            return
        
        # Tampilkan produk yang akan dinotifikasi
        if low_stock_products.exists():
            self.stdout.write('\n📦 Produk stok rendah:')
            for product in low_stock_products:
                self.stdout.write(f'   - {product.name}: {product.quantity}/{product.min_stock_level}')
        
        if out_of_stock_products.exists():
            self.stdout.write('\n📦 Produk stok habis:')
            for product in out_of_stock_products:
                self.stdout.write(f'   - {product.name}: {product.quantity}')
        
        # Konfirmasi pengiriman
        confirm = input(f'\n❓ Kirim email notifikasi ke {len(recipient_emails)} penerima? (y/N): ')
        if confirm.lower() != 'y':
            self.stdout.write('❌ Pengiriman email dibatalkan')
            return
        
        try:
            # Kirim email batch
            self.stdout.write('\n📧 Mengirim email notifikasi...')
            send_low_stock_email_notification()
            
            self.stdout.write(self.style.SUCCESS('✅ Email notifikasi berhasil dikirim!'))
            self.stdout.write(f'📧 Dikirim ke: {", ".join(recipient_emails)}')
            
            # Tampilkan info backend
            backend = getattr(settings, 'EMAIL_BACKEND', '')
            if 'console' in backend.lower():
                self.stdout.write('\n📺 Email muncul di console/terminal (Console Backend)')
                self.stdout.write('💡 Untuk kirim ke email sebenarnya, gunakan SMTP Backend')
            else:
                self.stdout.write('\n📧 Email dikirim ke alamat email sebenarnya (SMTP Backend)')
                self.stdout.write('💡 Cek inbox email penerima')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error mengirim email: {e}'))

    def send_test_email(self):
        self.stdout.write(self.style.SUCCESS('=== KIRIM EMAIL TEST ===\n'))
        
        # Cek email penerima
        recipient_emails = get_admin_and_owner_emails()
        if not recipient_emails:
            self.stdout.write(self.style.ERROR('❌ Tidak ada email penerima yang valid'))
            return
        
        self.stdout.write(f'📧 Email penerima: {", ".join(recipient_emails)}')
        
        # Konfirmasi pengiriman
        confirm = input(f'\n❓ Kirim email test ke {len(recipient_emails)} penerima? (y/N): ')
        if confirm.lower() != 'y':
            self.stdout.write('❌ Pengiriman email dibatalkan')
            return
        
        try:
            # Kirim email test
            self.stdout.write('\n📧 Mengirim email test...')
            
            send_mail(
                subject='[Stock Manager] Test Email dari Sistem',
                message='''Halo!

Ini adalah email test dari sistem Stock Manager.

Jika Anda menerima email ini, berarti:
✅ Sistem email berfungsi dengan baik
✅ Email Anda terdaftar sebagai penerima notifikasi
✅ Anda akan menerima notifikasi stok rendah secara otomatis

Terima kasih,
Stock Manager System

---
Email ini dikirim secara otomatis. Mohon jangan membalas email ini.''',
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=recipient_emails,
                fail_silently=False,
            )
            
            self.stdout.write(self.style.SUCCESS('✅ Email test berhasil dikirim!'))
            self.stdout.write(f'📧 Dikirim ke: {", ".join(recipient_emails)}')
            
            # Tampilkan info backend
            backend = getattr(settings, 'EMAIL_BACKEND', '')
            if 'console' in backend.lower():
                self.stdout.write('\n📺 Email muncul di console/terminal (Console Backend)')
                self.stdout.write('💡 Untuk kirim ke email sebenarnya, setup SMTP:')
                self.stdout.write('   python manage.py setup_email_smtp --gmail')
            else:
                self.stdout.write('\n📧 Email dikirim ke alamat email sebenarnya (SMTP Backend)')
                self.stdout.write('💡 Cek inbox email penerima')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error mengirim email: {e}'))
