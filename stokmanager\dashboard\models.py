from django.db import models
from django.utils import timezone

class Category(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name_plural = "Categories"

class UnitOfMeasure(models.Model):
    name = models.CharField(max_length=50)
    abbreviation = models.CharField(max_length=10)
    
    def __str__(self):
        return f"{self.name} ({self.abbreviation})"

class Product(models.Model):
    SIZE_CHOICES = (
        ('S', 'Small'),
        ('M', 'Medium'),
        ('L', 'Large'),
        ('XL', 'Extra Large'),
        ('XXL', 'Double Extra Large'),
        ('NA', 'Tidak Ada Ukuran'),
    )
    
    COLOR_CHOICES = (
        ('RED', 'Merah'),
        ('BLUE', 'Biru'),
        ('GREEN', 'Hijau'),
        ('BLACK', 'Hitam'),
        ('WHITE', 'Putih'),
        ('YELLOW', 'Kuning'),
        ('PURPLE', 'Ungu'),
        ('ORANGE', 'Oranye'),
        ('PINK', 'Pink'),
        ('BROWN', 'Coklat'),
        ('GRAY', 'Abu-abu'),
        ('MULTI', 'Multi Warna'),
        ('OTHER', 'Lainnya'),
    )
    
    code = models.CharField(max_length=50, unique=True, default='')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    quantity = models.IntegerField(default=0)
    min_stock_level = models.IntegerField(default=5)
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    selling_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    category = models.ForeignKey('Category', on_delete=models.SET_NULL, null=True, blank=True)
    unit = models.ForeignKey('UnitOfMeasure', on_delete=models.SET_NULL, null=True, blank=True)
    size = models.CharField(max_length=3, choices=SIZE_CHOICES, default='NA')
    color = models.CharField(max_length=10, choices=COLOR_CHOICES, default='OTHER')
    expiry_date = models.DateField(verbose_name='Tanggal Kedaluwarsa', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    @property
    def is_low_stock(self):
        """Check if product stock is low but not empty"""
        return self.quantity > 0 and self.quantity <= self.min_stock_level
    
    @property
    def is_out_of_stock(self):
        """Check if product is out of stock"""
        return self.quantity == 0

class StockMovement(models.Model):
    MOVEMENT_TYPES = (
        ('IN', 'Stock In'),
        ('OUT', 'Stock Out'),
    )
    
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.IntegerField()
    movement_type = models.CharField(max_length=3, choices=MOVEMENT_TYPES)
    timestamp = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.get_movement_type_display()} - {self.product.name} ({self.quantity})"
    
    def save(self, *args, **kwargs):
        # Simpan pergerakan stok
        super().save(*args, **kwargs)
        
        # Update stok produk jika belum diupdate
        product = self.product
        
        # Cek apakah stok rendah setelah pergerakan
        if product.is_low_stock:
            # Cek apakah sudah ada notifikasi yang belum dibaca untuk produk ini
            existing_notification = Notification.objects.filter(
                product=product,
                type='low_stock',
                status__in=['unread', 'read']
            ).exists()
            
            if not existing_notification:
                # Buat notifikasi stok rendah
                Notification.objects.create(
                    product=product,
                    type='low_stock',
                    message=f"Stok produk {product.name} rendah. Sisa stok: {product.quantity}, minimum: {product.min_stock_level}."
                )
        
        # Cek apakah stok habis setelah pergerakan
        if product.quantity == 0:
            # Cek apakah sudah ada notifikasi yang belum dibaca untuk produk ini
            existing_notification = Notification.objects.filter(
                product=product,
                type='out_of_stock',
                status__in=['unread', 'read']
            ).exists()
            
            if not existing_notification:
                # Buat notifikasi stok habis
                Notification.objects.create(
                    product=product,
                    type='out_of_stock',
                    message=f"Stok produk {product.name} habis. Segera lakukan restock."
                )
        
        # Buat pengingat restock jika stok masuk dan sebelumnya habis
        if self.movement_type == 'IN' and product.quantity > 0:
            # Cek apakah sebelumnya ada notifikasi stok habis
            out_of_stock_notification = Notification.objects.filter(
                product=product,
                type='out_of_stock',
                status__in=['unread', 'read']
            ).first()
            
            if out_of_stock_notification:
                # Ubah status notifikasi stok habis
                out_of_stock_notification.status = 'dismissed'
                out_of_stock_notification.save()
                
                # Buat notifikasi pengingat restock
                Notification.objects.create(
                    product=product,
                    type='restock_reminder',
                    message=f"Produk {product.name} telah di-restock. Stok saat ini: {product.quantity}."
                )

class Notification(models.Model):
    NOTIFICATION_TYPES = (
        ('low_stock', 'Stok Rendah'),
        ('out_of_stock', 'Stok Habis'),
        ('restock_reminder', 'Pengingat Restock'),
    )
    
    NOTIFICATION_STATUS = (
        ('unread', 'Belum Dibaca'),
        ('read', 'Sudah Dibaca'),
        ('dismissed', 'Diabaikan'),
    )
    
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='notifications')
    type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    message = models.TextField()
    status = models.CharField(max_length=10, choices=NOTIFICATION_STATUS, default='unread')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_type_display()} - {self.product.name}"
    
    def mark_as_read(self):
        """Tandai notifikasi sebagai sudah dibaca"""
        if self.status == 'unread':
            self.status = 'read'
            self.save()
    
    def dismiss(self):
        """Abaikan notifikasi"""
        self.status = 'dismissed'
        self.save()
    
    @property
    def is_unread(self):
        """Cek apakah notifikasi belum dibaca"""
        return self.status == 'unread'
    
    @property
    def is_read(self):
        """Cek apakah notifikasi sudah dibaca"""
        return self.status == 'read'
    
    @property
    def is_dismissed(self):
        """Cek apakah notifikasi diabaikan"""
        return self.status == 'dismissed'
    
    @property
    def type_icon(self):
        """Dapatkan ikon berdasarkan tipe notifikasi"""
        if self.type == 'low_stock':
            return 'fas fa-exclamation-triangle'
        elif self.type == 'out_of_stock':
            return 'fas fa-times-circle'
        elif self.type == 'restock_reminder':
            return 'fas fa-sync-alt'
        return 'fas fa-bell'
    
    @property
    def type_color(self):
        """Dapatkan warna berdasarkan tipe notifikasi"""
        if self.type == 'low_stock':
            return 'warning'
        elif self.type == 'out_of_stock':
            return 'danger'
        elif self.type == 'restock_reminder':
            return 'info'
        return 'primary'
