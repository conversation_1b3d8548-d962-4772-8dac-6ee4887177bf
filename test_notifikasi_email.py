#!/usr/bin/env python
"""
Script untuk testing sistem notifikasi email stok rendah
Jalankan dengan: python test_notifikasi_email.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stokmanager.settings')
django.setup()

from django.contrib.auth.models import User
from dashboard.models import Product, Category, UnitOfMeasure, StockMovement, Notification
from dashboard.utils import get_admin_and_owner_emails, send_low_stock_email_notification
from django.core.management import call_command
from django.db import models

def print_header(title):
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_step(step, description):
    print(f"\n[STEP {step}] {description}")
    print("-" * 40)

def test_admin_owner_setup():
    """Test 1: Cek setup admin dan owner"""
    print_step(1, "Mengecek Setup Admin dan Owner")
    
    # Cek admin users
    admin_users = User.objects.filter(is_staff=True, is_superuser=False)
    owner_users = User.objects.filter(is_superuser=True)
    
    print(f"Admin users: {admin_users.count()}")
    for user in admin_users:
        print(f"  - {user.username}: {user.email or 'TIDAK ADA EMAIL'} ({'AKTIF' if user.is_active else 'TIDAK AKTIF'})")
    
    print(f"Owner users: {owner_users.count()}")
    for user in owner_users:
        print(f"  - {user.username}: {user.email or 'TIDAK ADA EMAIL'} ({'AKTIF' if user.is_active else 'TIDAK AKTIF'})")
    
    # Cek email yang akan menerima notifikasi
    emails = get_admin_and_owner_emails()
    print(f"\nEmail penerima notifikasi: {len(emails)}")
    for email in emails:
        print(f"  - {email}")
    
    if not emails:
        print("\n⚠️  WARNING: Tidak ada email penerima yang valid!")
        print("   Buat user admin/owner dengan email atau update email yang ada.")
        return False
    
    print("\n✅ Setup admin/owner OK")
    return True

def test_product_setup():
    """Test 2: Setup produk untuk testing"""
    print_step(2, "Setup Produk untuk Testing")
    
    # Buat kategori jika belum ada
    category, created = Category.objects.get_or_create(
        name='Test Category',
        defaults={'description': 'Kategori untuk testing'}
    )
    if created:
        print(f"✅ Kategori '{category.name}' dibuat")
    
    # Buat unit jika belum ada
    unit, created = UnitOfMeasure.objects.get_or_create(
        name='Piece',
        defaults={'abbreviation': 'pcs'}
    )
    if created:
        print(f"✅ Unit '{unit.name}' dibuat")
    
    # Buat produk test jika belum ada
    product, created = Product.objects.get_or_create(
        code='TEST001',
        defaults={
            'name': 'Produk Test Email',
            'description': 'Produk untuk testing email notifikasi',
            'quantity': 20,
            'min_stock_level': 5,
            'category': category,
            'unit': unit,
            'purchase_price': 10000,
            'selling_price': 15000,
        }
    )
    
    if created:
        print(f"✅ Produk '{product.name}' dibuat")
    else:
        print(f"✅ Produk '{product.name}' sudah ada")
    
    print(f"   Stok saat ini: {product.quantity}")
    print(f"   Min stock level: {product.min_stock_level}")
    
    return product

def test_low_stock_notification(product):
    """Test 3: Test notifikasi stok rendah"""
    print_step(3, "Test Notifikasi Stok Rendah")
    
    # Pastikan stok di atas minimum dulu
    if product.quantity <= product.min_stock_level:
        # Tambah stok dulu
        StockMovement.objects.create(
            product=product,
            quantity=10,
            movement_type='IN',
            notes='Restock untuk testing'
        )
        product.refresh_from_db()
        print(f"✅ Stok ditambah menjadi: {product.quantity}")
    
    # Hitung berapa yang harus dikeluarkan untuk membuat stok rendah
    target_stock = product.min_stock_level  # Tepat di minimum
    quantity_out = product.quantity - target_stock
    
    if quantity_out > 0:
        print(f"📦 Mengeluarkan {quantity_out} unit untuk membuat stok rendah...")
        
        # Buat stock movement OUT
        movement = StockMovement.objects.create(
            product=product,
            quantity=quantity_out,
            movement_type='OUT',
            notes='Testing stok rendah'
        )
        
        product.refresh_from_db()
        print(f"✅ Stok sekarang: {product.quantity} (minimum: {product.min_stock_level})")
        
        # Cek apakah notifikasi dibuat
        notification = Notification.objects.filter(
            product=product,
            type='low_stock',
            status__in=['unread', 'read']
        ).first()
        
        if notification:
            print(f"✅ Notifikasi stok rendah dibuat: {notification.message}")
            print("📧 Email notifikasi seharusnya sudah dikirim otomatis")
        else:
            print("❌ Notifikasi stok rendah tidak dibuat")
            return False
    else:
        print("ℹ️  Stok sudah rendah, tidak perlu stock movement")
    
    return True

def test_out_of_stock_notification(product):
    """Test 4: Test notifikasi stok habis"""
    print_step(4, "Test Notifikasi Stok Habis")
    
    if product.quantity > 0:
        print(f"📦 Mengeluarkan {product.quantity} unit untuk menghabiskan stok...")
        
        # Buat stock movement OUT untuk menghabiskan stok
        movement = StockMovement.objects.create(
            product=product,
            quantity=product.quantity,
            movement_type='OUT',
            notes='Testing stok habis'
        )
        
        product.refresh_from_db()
        print(f"✅ Stok sekarang: {product.quantity}")
        
        # Cek apakah notifikasi dibuat
        notification = Notification.objects.filter(
            product=product,
            type='out_of_stock',
            status__in=['unread', 'read']
        ).first()
        
        if notification:
            print(f"✅ Notifikasi stok habis dibuat: {notification.message}")
            print("📧 Email notifikasi seharusnya sudah dikirim otomatis")
        else:
            print("❌ Notifikasi stok habis tidak dibuat")
            return False
    else:
        print("ℹ️  Stok sudah habis")
    
    return True

def test_manual_commands():
    """Test 5: Test management commands"""
    print_step(5, "Test Management Commands")
    
    print("🔧 Testing command: check_stock")
    try:
        call_command('check_stock')
        print("✅ Command check_stock berhasil")
    except Exception as e:
        print(f"❌ Command check_stock error: {e}")
    
    print("\n🔧 Testing command: send_stock_notifications --batch")
    try:
        call_command('send_stock_notifications', batch=True)
        print("✅ Command send_stock_notifications --batch berhasil")
    except Exception as e:
        print(f"❌ Command send_stock_notifications error: {e}")
    
    print("\n🔧 Testing command: test_email_notification --check-emails")
    try:
        call_command('test_email_notification', check_emails=True)
        print("✅ Command test_email_notification --check-emails berhasil")
    except Exception as e:
        print(f"❌ Command test_email_notification error: {e}")

def test_email_function():
    """Test 6: Test fungsi email langsung"""
    print_step(6, "Test Fungsi Email Langsung")
    
    try:
        print("📧 Mengirim email batch untuk semua produk stok rendah/habis...")
        send_low_stock_email_notification()
        print("✅ Fungsi email batch berhasil")
    except Exception as e:
        print(f"❌ Fungsi email batch error: {e}")
    
    # Test email untuk produk tertentu
    product = Product.objects.filter(quantity__lte=models.F('min_stock_level')).first()
    if product:
        try:
            print(f"📧 Mengirim email untuk produk: {product.name}")
            send_low_stock_email_notification(product=product)
            print("✅ Fungsi email produk tertentu berhasil")
        except Exception as e:
            print(f"❌ Fungsi email produk tertentu error: {e}")

def cleanup():
    """Cleanup data testing"""
    print_step("CLEANUP", "Membersihkan Data Testing")
    
    # Hapus notifikasi test
    test_notifications = Notification.objects.filter(
        product__code='TEST001'
    )
    count = test_notifications.count()
    test_notifications.delete()
    print(f"✅ Dihapus {count} notifikasi testing")
    
    # Reset stok produk test
    try:
        product = Product.objects.get(code='TEST001')
        product.quantity = 20  # Reset ke stok awal
        product.save()
        print(f"✅ Stok produk {product.name} direset ke {product.quantity}")
    except Product.DoesNotExist:
        pass

def main():
    print_header("TESTING SISTEM NOTIFIKASI EMAIL STOK RENDAH")
    
    # Test 1: Setup admin/owner
    if not test_admin_owner_setup():
        print("\n❌ Testing dihentikan: Setup admin/owner tidak valid")
        return
    
    # Test 2: Setup produk
    product = test_product_setup()
    
    # Test 3: Notifikasi stok rendah
    test_low_stock_notification(product)
    
    # Test 4: Notifikasi stok habis
    test_out_of_stock_notification(product)
    
    # Test 5: Management commands
    test_manual_commands()
    
    # Test 6: Fungsi email langsung
    test_email_function()
    
    # Cleanup
    cleanup()
    
    print_header("TESTING SELESAI")
    print("✅ Semua test telah dijalankan")
    print("\n📧 Cek console/terminal untuk melihat email yang dikirim")
    print("   (jika menggunakan console email backend)")
    
    print("\n📝 Catatan:")
    print("   - Pastikan email settings sudah benar untuk production")
    print("   - Monitor log untuk error email")
    print("   - Setup cron job untuk pengecekan otomatis")

if __name__ == '__main__':
    main()
