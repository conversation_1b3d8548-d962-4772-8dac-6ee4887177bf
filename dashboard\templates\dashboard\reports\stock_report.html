{% extends "dashboard/base.html" %}

{% block title %}<PERSON><PERSON><PERSON>{% endblock %}

{% block head_extras %}
<style>
    .report-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .report-header {
        margin-bottom: 20px;
    }
    
    .report-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .report-subtitle {
        font-size: 16px;
        color: #6c757d;
        margin-bottom: 20px;
    }
    
    .filter-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        padding: 15px 20px;
    }
    
    .chart-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .chart-header {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .chart-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }
    
    .chart-body {
        padding: 20px;
        position: relative;
        min-height: 300px;
    }
    
    .table-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .table-header {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .table-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }
    
    .table-body {
        padding: 0;
    }
    
    .table {
        margin-bottom: 0;
    }
    
    .table th {
        font-weight: 600;
        background-color: #f8f9fa;
    }
    
    .badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
    }
    
    .badge-danger {
        background-color: #dc3545;
        color: #fff;
    }
    
    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }
    
    .form-select {
        display: block;
        width: 100%;
        padding: 0.375rem 2.25rem 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 16px 12px;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        appearance: none;
    }
    
    .form-label {
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    
    .form-select:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    
    .btn-export {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-export:hover {
        background-color: #218838;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="report-container">
        <div class="report-header">
            <h1 class="report-title">Laporan Stok</h1>
            <p class="report-subtitle">Analisis pergerakan stok dan status produk</p>
        </div>
        
        <!-- Filter -->
        <div class="filter-card">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="period" class="form-label">Periode</label>
                    <select name="period" id="period" class="form-select" onchange="this.form.submit()">
                        <option value="daily" {% if period == 'daily' %}selected{% endif %}>Harian (7 hari terakhir)</option>
                        <option value="weekly" {% if period == 'weekly' %}selected{% endif %}>Mingguan</option>
                        <option value="monthly" {% if period == 'monthly' %}selected{% endif %}>Bulanan</option>
                    </select>
                </div>
                <div class="col-md-4 ms-auto text-end">
                    <a href="{% url 'dashboard:stock_report_export_pdf' %}?period={{ period }}" class="btn-export" target="_blank">
                        <i class="fas fa-file-pdf"></i> Export PDF
                    </a>
                    <a href="{% url 'dashboard:stock_report_export_excel' %}?period={{ period }}" class="btn-export" target="_blank">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </a>
                </div>
            </form>
        </div>
        
        <!-- Grafik Stok -->
        <div class="chart-card">
            <div class="chart-header">
                <h5 class="chart-title">Grafik Pergerakan Stok</h5>
                <div class="chart-actions">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleChartType()">
                        <i class="fas fa-chart-line"></i> Ubah Tipe Grafik
                    </button>
                    <!-- Tombol export PDF dan Excel dihapus -->
                </div>
            </div>
            <div class="chart-body">
                <canvas id="stockChart" height="300"></canvas>
            </div>
        </div>
        
        <div class="row">
            <!-- Produk dengan Stok Rendah -->
            <div class="col-md-6">
                <div class="table-card">
                    <div class="table-header">
                        <h5 class="table-title">Produk dengan Stok Rendah</h5>
                    </div>
                    <div class="table-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Produk</th>
                                    <th>Stok</th>
                                    <th>Min. Stok</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>
                                        <a href="{% url 'dashboard:product_detail' product.id %}">
                                            {{ product.name }}
                                        </a>
                                    </td>
                                    <td>{{ product.quantity }}</td>
                                    <td>{{ product.min_stock_level }}</td>
                                    <td>
                                        {% if product.quantity == 0 %}
                                        <span class="badge badge-danger">
                                            <i class="fas fa-times-circle"></i> Habis
                                        </span>
                                        {% else %}
                                        <span class="badge badge-warning">
                                            <i class="fas fa-exclamation-triangle"></i> Rendah
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">Tidak ada produk dengan stok rendah</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Produk dengan Stok Tertinggi -->
            <div class="col-md-6">
                <div class="table-card">
                    <div class="table-header">
                        <h5 class="table-title">Produk dengan Stok Tertinggi</h5>
                    </div>
                    <div class="table-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Produk</th>
                                    <th>Stok</th>
                                    <th>Min. Stok</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in high_stock_products %}
                                <tr>
                                    <td>
                                        <a href="{% url 'dashboard:product_detail' product.id %}">
                                            {{ product.name }}
                                        </a>
                                    </td>
                                    <td>{{ product.quantity }}</td>
                                    <td>{{ product.min_stock_level }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">Tidak ada data produk</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Data JSON untuk JavaScript -->
<script id="stock-chart-data" type="application/json">
{
    "periods": {{ periods_json|safe|default:"[]" }},
    "stockInData": {{ stock_in_json|safe|default:"[]" }},
    "stockOutData": {{ stock_out_json|safe|default:"[]" }}
}
</script>

<script>
    let stockChart;
    let currentChartType = 'line'; // Changed from 'bar' to 'line'
    
    document.addEventListener('DOMContentLoaded', function() {
        // Ambil data dari script tag
        const stockDataElement = document.getElementById('stock-chart-data');
        const stockData = JSON.parse(stockDataElement.textContent);
        
        const periods = stockData.periods;
        const stockInData = stockData.stockInData;
        const stockOutData = stockData.stockOutData;
        
        // Pastikan elemen canvas ada
        const chartElement = document.getElementById('stockChart');
        if (chartElement) {
            const ctx = chartElement.getContext('2d');
            
            // Buat grafik
            stockChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: periods,
                    datasets: [
                        {
                            label: 'Stok Masuk',
                            data: stockInData,
                            backgroundColor: 'rgba(75, 192, 192, 0.5)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                            pointRadius: 4,
                            pointHoverRadius: 6
                        },
                        {
                            label: 'Stok Keluar',
                            data: stockOutData,
                            backgroundColor: 'rgba(255, 99, 132, 0.5)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                            pointRadius: 4,
                            pointHoverRadius: 6
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        }
    });
    
    // Fungsi untuk mengubah tipe grafik
    function toggleChartType() {
        if (!stockChart) return;
        
        currentChartType = currentChartType === 'line' ? 'bar' : 'line'; // Changed order
        stockChart.config.type = currentChartType;
        stockChart.update();
    }
    
    // Fungsi untuk export ke PDF
    function exportToPDF() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('landscape');
        
        // Judul laporan
        doc.setFontSize(18);
        doc.text('Laporan Stok', 14, 22);
        
        // Periode
        const periodSelect = document.getElementById('period');
        const periodText = periodSelect.options[periodSelect.selectedIndex].text;
        doc.setFontSize(12);
        doc.text(`Periode: ${periodText}`, 14, 30);
        
        // Tambahkan tanggal laporan
        const today = new Date();
        doc.text(`Tanggal Laporan: ${today.toLocaleDateString('id-ID')}`, 14, 38);
        
        // Ambil data dari tabel stok rendah
        const lowStockTable = document.querySelector('.table-card:nth-child(1) table');
        if (lowStockTable) {
            doc.setFontSize(14);
            doc.text('Produk dengan Stok Rendah', 14, 50);
            
            doc.autoTable({
                html: lowStockTable,
                startY: 55,
                theme: 'grid',
                headStyles: { fillColor: [66, 66, 66] },
                margin: { left: 14, right: 14 }
            });
        }
        
        // Ambil data dari tabel stok tertinggi
        const highStockTable = document.querySelector('.table-card:nth-child(2) table');
        if (highStockTable) {
            const finalY = doc.lastAutoTable.finalY + 15;
            
            doc.setFontSize(14);
            doc.text('Produk dengan Stok Tertinggi', 14, finalY);
            
            doc.autoTable({
                html: highStockTable,
                startY: finalY + 5,
                theme: 'grid',
                headStyles: { fillColor: [66, 66, 66] },
                margin: { left: 14, right: 14 }
            });
        }
        
        // Tambahkan grafik
        if (stockChart) {
            const finalY = doc.lastAutoTable.finalY + 15;
            doc.setFontSize(14);
            doc.text('Grafik Pergerakan Stok', 14, finalY);
            
            // Konversi grafik ke gambar
            const chartImage = stockChart.toBase64Image();
            doc.addImage(chartImage, 'PNG', 14, finalY + 5, 270, 120);
        }
        
        // Simpan PDF
        doc.save('laporan-stok.pdf');
    }
    
    // Fungsi untuk export ke Excel
    function exportToExcel() {
        // Buat workbook baru
        const wb = XLSX.utils.book_new();
        
        // Ambil data dari tabel stok rendah
        const lowStockTable = document.querySelector('.table-card:nth-child(1) table');
        if (lowStockTable) {
            const lowStockData = tableToArray(lowStockTable);
            const lowStockWs = XLSX.utils.aoa_to_sheet(lowStockData);
            XLSX.utils.book_append_sheet(wb, lowStockWs, "Stok Rendah");
        }
        
        // Ambil data dari tabel stok tertinggi
        const highStockTable = document.querySelector('.table-card:nth-child(2) table');
        if (highStockTable) {
            const highStockData = tableToArray(highStockTable);
            const highStockWs = XLSX.utils.aoa_to_sheet(highStockData);
            XLSX.utils.book_append_sheet(wb, highStockWs, "Stok Tertinggi");
        }
        
        // Buat sheet untuk data grafik
        if (stockChart) {
            const chartData = [
                ['Periode', 'Stok Masuk', 'Stok Keluar']
            ];
            
            const labels = stockChart.data.labels;
            const stockInData = stockChart.data.datasets[0].data;
            const stockOutData = stockChart.data.datasets[1].data;
            
            for (let i = 0; i < labels.length; i++) {
                chartData.push([labels[i], stockInData[i], stockOutData[i]]);
            }
            
            const chartWs = XLSX.utils.aoa_to_sheet(chartData);
            XLSX.utils.book_append_sheet(wb, chartWs, "Data Grafik");
        }
        
        // Simpan file Excel
        XLSX.writeFile(wb, "laporan-stok.xlsx");
    }
    
    // Fungsi untuk mengkonversi tabel HTML ke array
    function tableToArray(table) {
        const rows = table.querySelectorAll('tr');
        const data = [];
        
        rows.forEach(row => {
            const rowData = [];
            const cells = row.querySelectorAll('th, td');
            
            cells.forEach(cell => {
                // Hapus link dan ambil teks saja
                rowData.push(cell.textContent.trim());
            });
            
            data.push(rowData);
        });
        
        return data;
    }
</script>
{% endblock %}








