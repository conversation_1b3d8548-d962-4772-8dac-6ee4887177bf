from django.core.management.base import BaseCommand
from django.conf import settings
import os

class Command(BaseCommand):
    help = 'Setup konfigurasi email SMTP untuk mengirim email ke alamat email yang sebenarnya'

    def add_arguments(self, parser):
        parser.add_argument(
            '--gmail',
            action='store_true',
            help='Setup untuk Gmail SMTP',
        )
        parser.add_argument(
            '--test-smtp',
            action='store_true',
            help='Test koneksi SMTP',
        )

    def handle(self, *args, **options):
        if options['gmail']:
            self.setup_gmail()
        elif options['test_smtp']:
            self.test_smtp()
        else:
            self.show_current_config()

    def show_current_config(self):
        self.stdout.write(self.style.SUCCESS('=== KONFIGURASI EMAIL SAAT INI ===\n'))
        
        backend = getattr(settings, 'EMAIL_BACKEND', 'Tidak diset')
        self.stdout.write(f'EMAIL_BACKEND: {backend}')
        
        if 'console' in backend.lower():
            self.stdout.write(self.style.WARNING('📧 Saat ini menggunakan Console Backend'))
            self.stdout.write('   Email hanya muncul di terminal, tidak dikirim ke email sebenarnya')
            self.stdout.write('\n💡 Untuk mengirim email ke alamat sebenarnya:')
            self.stdout.write('   python manage.py setup_email_smtp --gmail')
        elif 'smtp' in backend.lower():
            host = getattr(settings, 'EMAIL_HOST', 'Tidak diset')
            port = getattr(settings, 'EMAIL_PORT', 'Tidak diset')
            use_tls = getattr(settings, 'EMAIL_USE_TLS', 'Tidak diset')
            user = getattr(settings, 'EMAIL_HOST_USER', 'Tidak diset')
            self.stdout.write(self.style.SUCCESS('📧 Menggunakan SMTP Backend'))
            self.stdout.write(f'   HOST: {host}')
            self.stdout.write(f'   PORT: {port}')
            self.stdout.write(f'   TLS: {use_tls}')
            self.stdout.write(f'   USER: {user}')
            self.stdout.write('\n💡 Test koneksi SMTP:')
            self.stdout.write('   python manage.py setup_email_smtp --test-smtp')

    def setup_gmail(self):
        self.stdout.write(self.style.SUCCESS('=== SETUP GMAIL SMTP ===\n'))
        
        self.stdout.write('📧 Untuk menggunakan Gmail SMTP, Anda perlu:')
        self.stdout.write('1. Mengaktifkan 2-Factor Authentication di akun Gmail')
        self.stdout.write('2. Membuat App Password khusus untuk aplikasi ini')
        self.stdout.write('3. Menggunakan App Password tersebut, bukan password Gmail biasa')
        
        self.stdout.write('\n🔧 Langkah-langkah:')
        self.stdout.write('1. Buka https://myaccount.google.com/security')
        self.stdout.write('2. Aktifkan 2-Step Verification')
        self.stdout.write('3. Buat App Password untuk "Mail"')
        self.stdout.write('4. Salin App Password yang diberikan')
        
        email = input('\n📧 Masukkan email Gmail Anda: ')
        app_password = input('🔑 Masukkan App Password Gmail: ')
        
        if email and app_password:
            self.create_smtp_settings(email, app_password)
        else:
            self.stdout.write(self.style.ERROR('❌ Email dan App Password harus diisi'))

    def create_smtp_settings(self, email, app_password):
        settings_content = f'''
# Email settings untuk SMTP Gmail
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '{email}'
EMAIL_HOST_PASSWORD = '{app_password}'
DEFAULT_FROM_EMAIL = '{email}'

# Untuk kembali ke console backend (development), uncomment baris di bawah:
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
'''
        
        # Tulis ke file terpisah
        with open('email_smtp_settings.py', 'w') as f:
            f.write(settings_content)
        
        self.stdout.write(self.style.SUCCESS('\n✅ Konfigurasi SMTP berhasil dibuat!'))
        self.stdout.write(f'📄 File konfigurasi: email_smtp_settings.py')
        
        self.stdout.write('\n🔧 Untuk mengaktifkan:')
        self.stdout.write('1. Buka file settings.py')
        self.stdout.write('2. Ganti konfigurasi email yang ada dengan:')
        self.stdout.write(f'   EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"')
        self.stdout.write(f'   EMAIL_HOST = "smtp.gmail.com"')
        self.stdout.write(f'   EMAIL_PORT = 587')
        self.stdout.write(f'   EMAIL_USE_TLS = True')
        self.stdout.write(f'   EMAIL_HOST_USER = "{email}"')
        self.stdout.write(f'   EMAIL_HOST_PASSWORD = "{app_password}"')
        self.stdout.write(f'   DEFAULT_FROM_EMAIL = "{email}"')
        
        self.stdout.write('\n⚠️  PENTING:')
        self.stdout.write('   - Jangan commit App Password ke Git/repository')
        self.stdout.write('   - Gunakan environment variables untuk production')
        self.stdout.write('   - Simpan App Password di tempat yang aman')

    def test_smtp(self):
        self.stdout.write(self.style.SUCCESS('=== TEST KONEKSI SMTP ===\n'))
        
        backend = getattr(settings, 'EMAIL_BACKEND', '')
        if 'console' in backend.lower():
            self.stdout.write(self.style.ERROR('❌ Masih menggunakan Console Backend'))
            self.stdout.write('   Ubah ke SMTP Backend dulu untuk test koneksi')
            return
        
        try:
            from django.core.mail import get_connection
            from dashboard.utils import get_admin_and_owner_emails
            
            # Test koneksi
            self.stdout.write('🔌 Testing koneksi SMTP...')
            connection = get_connection()
            connection.open()
            self.stdout.write(self.style.SUCCESS('✅ Koneksi SMTP berhasil'))
            connection.close()
            
            # Test kirim email
            recipient_emails = get_admin_and_owner_emails()
            if recipient_emails:
                self.stdout.write(f'\n📧 Testing kirim email ke {len(recipient_emails)} penerima...')
                
                from django.core.mail import send_mail
                send_mail(
                    subject='[Stock Manager] Test SMTP Connection',
                    message='Ini adalah email test untuk memastikan koneksi SMTP berfungsi dengan baik.\n\nJika Anda menerima email ini, berarti konfigurasi SMTP sudah benar.',
                    from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', settings.EMAIL_HOST_USER),
                    recipient_list=recipient_emails,
                    fail_silently=False,
                )
                
                self.stdout.write(self.style.SUCCESS('✅ Email test berhasil dikirim'))
                self.stdout.write(f'📧 Cek inbox email: {", ".join(recipient_emails)}')
            else:
                self.stdout.write(self.style.WARNING('⚠️  Tidak ada email penerima yang valid'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error testing SMTP: {e}'))
            self.stdout.write('\n🔧 Kemungkinan masalah:')
            self.stdout.write('   - Email atau App Password salah')
            self.stdout.write('   - 2-Factor Authentication belum diaktifkan')
            self.stdout.write('   - App Password belum dibuat')
            self.stdout.write('   - Koneksi internet bermasalah')
            self.stdout.write('   - Firewall memblokir koneksi SMTP')
