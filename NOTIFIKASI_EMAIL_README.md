# Sistem Notifikasi Email Stok Rendah

Sistem ini secara otomatis mengirimkan notifikasi email kepada admin dan owner ketika stok produk rendah atau habis.

## Fitur Utama

1. **Notifikasi Otomatis**: Email dikirim secara otomatis ketika:
   - Stok produk mencapai atau di bawah level minimum (`min_stock_level`)
   - Stok produk habis (quantity = 0)

2. **Target Penerima**: Email dikirim ke semua user dengan role:
   - **Admin**: User dengan `is_staff=True` dan `is_superuser=False`
   - **Owner**: User dengan `is_superuser=True`

3. **Integrasi Seamless**: Terintegrasi dengan sistem yang sudah ada tanpa merusak fungsi lain

## Cara Kerja

### 1. Notifikasi Otomatis Saat Transaksi Stok
Ketika ada pergerakan stok (StockMovement), sistem akan:
- <PERSON><PERSON><PERSON><PERSON> apakah stok produk menjadi rendah atau habis
- Membuat notifikasi internal di database
- **OTOMATIS mengirim email** ke semua admin dan owner

### 2. Notifikasi Manual via Management Command
```bash
# Cek stok dan kirim email untuk produk yang baru ditemukan stok rendah/habis
python manage.py check_stock

# Kirim email batch untuk semua produk stok rendah/habis saat ini
python manage.py send_stock_notifications --batch

# Kirim email individual untuk setiap produk stok rendah/habis
python manage.py send_stock_notifications
```

### 3. Testing Sistem Email
```bash
# Cek email admin dan owner yang terdaftar
python manage.py test_email_notification --check-emails

# Kirim email test
python manage.py test_email_notification --send-test
```

## Konfigurasi Email

### Development (Default)
```python
# settings.py
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```
Email akan ditampilkan di console/terminal.

### Production (SMTP)
```python
# settings.py
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
DEFAULT_FROM_EMAIL = '<EMAIL>'
```

## Persyaratan

1. **User Admin/Owner harus memiliki email**: 
   - Email tidak boleh kosong
   - User harus aktif (`is_active=True`)

2. **Konfigurasi Email**: 
   - Untuk production, pastikan SMTP settings sudah benar
   - Untuk development, email akan muncul di console

## Template Email

Template email tersimpan di: `dashboard/templates/dashboard/emails/low_stock_notification.html`

Template ini menampilkan:
- Daftar produk dengan stok rendah
- Daftar produk dengan stok habis
- Detail stok saat ini dan minimum
- Format yang user-friendly

## Troubleshooting

### 1. Email tidak terkirim
```bash
# Cek apakah ada admin/owner dengan email valid
python manage.py test_email_notification --check-emails
```

### 2. Error SMTP
- Pastikan konfigurasi SMTP benar
- Untuk Gmail, gunakan App Password bukan password biasa
- Cek firewall dan koneksi internet

### 3. Notifikasi tidak muncul
- Pastikan ada pergerakan stok (StockMovement)
- Cek apakah sudah ada notifikasi untuk produk tersebut
- Jalankan `python manage.py check_stock` secara manual

## Integrasi dengan Sistem Lain

### Cron Job (Opsional)
Untuk menjalankan pengecekan stok secara berkala:

```bash
# Tambahkan ke crontab untuk cek setiap jam
0 * * * * cd /path/to/project && python manage.py check_stock

# Atau kirim email batch setiap hari jam 9 pagi
0 9 * * * cd /path/to/project && python manage.py send_stock_notifications --batch
```

### API Integration (Future)
Sistem ini dapat diperluas untuk:
- Webhook notifications
- SMS notifications
- Slack/Discord integration
- Mobile push notifications

## File yang Dimodifikasi

1. `dashboard/utils.py` - Fungsi email dan utilitas
2. `dashboard/models.py` - Integrasi email di StockMovement.save()
3. `dashboard/management/commands/check_stock.py` - Tambah email otomatis
4. `dashboard/management/commands/send_stock_notifications.py` - Enhanced command
5. `dashboard/management/commands/test_email_notification.py` - Command testing baru

## Keamanan

- Email hanya dikirim ke user dengan role admin/owner
- Tidak ada data sensitif dalam email (hanya info stok)
- Template email aman dari XSS
- Sistem fail-safe: error email tidak mengganggu operasi normal
