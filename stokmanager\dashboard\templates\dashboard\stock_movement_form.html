{% extends "dashboard/base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block head_extras %}
<style>
    /* Form styling */
    .form-container {
        max-width: 900px;
        margin: 0 auto;
    }
    
    .form-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .form-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .form-header h4 {
        margin: 0;
        font-size: 20px;
        color: #333;
        font-weight: 600;
    }
    
    .form-body {
        padding: 25px;
    }
    
    .form-section {
        margin-bottom: 25px;
        padding-bottom: 25px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .form-section:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }
    
    .form-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .form-row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -10px;
        margin-left: -10px;
    }
    
    .form-group {
        padding-right: 10px;
        padding-left: 10px;
        margin-bottom: 20px;
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    @media (min-width: 768px) {
        .form-group.col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
        
        .form-group.col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }
        
        .form-group.col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }
    }
    
    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #555;
        font-size: 14px;
    }
    
    .form-control {
        display: block;
        width: 100%;
        padding: 10px 15px;
        font-size: 14px;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    textarea.form-control {
        height: auto;
        resize: vertical;
        min-height: 100px;
    }
    
    select.form-control {
        padding-right: 30px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='4' viewBox='0 0 8 4'%3E%3Cpath fill='%23343a40' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 8px 4px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }
    
    .form-text {
        display: block;
        margin-top: 5px;
        font-size: 12px;
        color: #6c757d;
    }
    
    .form-footer {
        padding: 20px;
        background-color: #f8f9fa;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 10px 20px;
        font-size: 14px;
        line-height: 1.5;
        border-radius: 4px;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        cursor: pointer;
    }
    
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .btn-primary:hover {
        color: #fff;
        background-color: #0069d9;
        border-color: #0062cc;
    }
    
    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }
    
    .btn-secondary:hover {
        color: #fff;
        background-color: #5a6268;
        border-color: #545b62;
    }
    
    .btn-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    .btn-icon i {
        margin-right: 8px;
    }
    
    .text-danger {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
    }
    
    /* Stock movement specific styling */
    .stock-in-icon {
        color: #28a745;
    }
    
    .stock-out-icon {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="form-container">
        <div class="form-card">
            <div class="form-header">
                <h4>
                    {% if form.movement_type.value == 'IN' %}
                    <i class="fas fa-plus-circle stock-in-icon"></i>
                    {% else %}
                    <i class="fas fa-minus-circle stock-out-icon"></i>
                    {% endif %}
                    {{ title }}
                </h4>
            </div>
            <form method="post" class="needs-validation" novalidate>
                {% csrf_token %}
                {{ form.movement_type }}
                
                <div class="form-body">
                    <div class="form-section">
                        <h5 class="form-section-title">Informasi Produk</h5>
                        <div class="form-group">
                            <label for="{{ form.product.id_for_label }}" class="form-label">Produk</label>
                            {{ form.product }}
                            {% if form.product.errors %}
                            <div class="text-danger">{{ form.product.errors }}</div>
                            {% endif %}
                            <small class="form-text">Pilih produk yang akan diubah stoknya</small>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h5 class="form-section-title">Detail Pergerakan Stok</h5>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="{{ form.quantity.id_for_label }}" class="form-label">Jumlah</label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                <div class="text-danger">{{ form.quantity.errors }}</div>
                                {% endif %}
                                <small class="form-text">
                                    {% if form.movement_type.value == 'IN' %}
                                    Jumlah stok yang akan ditambahkan
                                    {% else %}
                                    Jumlah stok yang akan dikurangi
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Catatan</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                            {% endif %}
                            <small class="form-text">Tambahkan catatan atau keterangan (opsional)</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-footer">
                    <a href="{% url 'dashboard:stock_movement_list' %}" class="btn btn-secondary btn-icon">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                    <button type="submit" class="btn btn-primary btn-icon">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Tambahkan class form-control ke semua input
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
        inputs.forEach(input => {
            input.classList.add('form-control');
        });
        
        // Tambahkan class form-check-input ke checkbox
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.classList.add('form-check-input');
        });
        
        // Form validation
        const form = document.querySelector('.needs-validation');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
</script>
{% endblock %}
