{% extends "dashboard/base.html" %}
{% load humanize %}

{% block title %}Daftar Produk{% endblock %}

{% block head_extras %}
<style>
    /* Styling untuk halaman produk */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .page-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }
    
    .btn-add {
        background-color: #3498db;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: background-color 0.3s;
    }
    
    .btn-add:hover {
        background-color: #2980b9;
    }
    
    .btn-add i {
        margin-right: 8px;
    }
    
    /* Statistik cards */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        border-radius: 8px;
        padding: 15px;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
    
    .stat-card.blue {
        background-color: #3498db;
        border-left: 5px solid #2980b9;
    }
    
    .stat-card.green {
        background-color: #2ecc71;
        border-left: 5px solid #27ae60;
    }
    
    .stat-card.yellow {
        background-color: #f39c12;
        border-left: 5px solid #e67e22;
    }
    
    .stat-card.red {
        background-color: #e74c3c;
        border-left: 5px solid #c0392b;
    }
    
    .stat-icon {
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }
    
    .stat-icon i {
        font-size: 24px;
    }
    
    .stat-info h5 {
        font-size: 14px;
        margin: 0 0 5px 0;
        opacity: 0.8;
    }
    
    .stat-info h2 {
        font-size: 28px;
        margin: 0;
        font-weight: bold;
    }
    
    /* Filter box */
    .filter-box {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .filter-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        font-size: 14px;
        color: #555;
    }
    
    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s;
    }
    
    .form-control:focus {
        border-color: #3498db;
        outline: none;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
    }
    
    .input-icon-wrapper {
        position: relative;
    }
    
    .input-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
    }
    
    .input-with-icon {
        padding-left: 35px;
    }
    
    .form-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 15px;
    }
    
    .btn {
        padding: 8px 16px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        font-size: 14px;
        display: inline-flex;
        align-items: center;
        transition: background-color 0.3s;
    }
    
    .btn i {
        margin-right: 8px;
    }
    
    .btn-primary {
        background-color: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background-color: #2980b9;
    }
    
    .btn-secondary {
        background-color: #95a5a6;
        color: white;
    }
    
    .btn-secondary:hover {
        background-color: #7f8c8d;
    }
    
    /* Table styling */
    .table-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .data-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .data-table th {
        background-color: #f8f9fa;
        padding: 12px 15px;
        text-align: left;
        font-weight: 600;
        font-size: 13px;
        color: #555;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .data-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #e0e0e0;
        font-size: 14px;
        color: #333;
    }
    
    .data-table tr:hover {
        background-color: #f5f5f5;
    }
    
    .data-table tr:last-child td {
        border-bottom: none;
    }
    
    /* Status badges */
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 50px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .badge i {
        margin-right: 4px;
    }
    
    .badge-success {
        background-color: #d4edda;
        color: #155724;
    }
    
    .badge-warning {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .badge-danger {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    /* Action buttons */
    .action-buttons {
        display: flex;
        gap: 10px;
    }
    
    .btn-icon {
        color: #555;
        font-size: 16px;
        transition: color 0.3s;
    }
    
    .btn-icon.view {
        color: #3498db;
    }
    
    .btn-icon.view:hover {
        color: #2980b9;
    }
    
    .btn-icon.edit {
        color: #f39c12;
    }
    
    .btn-icon.edit:hover {
        color: #e67e22;
    }
    
    .btn-icon.delete {
        color: #e74c3c;
    }
    
    .btn-icon.delete:hover {
        color: #c0392b;
    }
    
    .btn-icon.stock-in {
        color: #2ecc71;
    }
    
    .btn-icon.stock-in:hover {
        color: #27ae60;
    }
    
    .btn-icon.stock-out {
        color: #9b59b6;
    }
    
    .btn-icon.stock-out:hover {
        color: #8e44ad;
    }
    
    /* Empty state */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
    }
    
    .empty-state i {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 15px;
    }
    
    .empty-state p {
        color: #777;
        margin-bottom: 15px;
    }
    
    /* Pagination */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }
    
    .pagination-list {
        display: inline-flex;
        list-style: none;
        padding: 0;
        margin: 0;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .pagination-item {
        border: 1px solid #ddd;
        margin-left: -1px;
    }
    
    .pagination-item:first-child {
        margin-left: 0;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }
    
    .pagination-item:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }
    
    .pagination-link {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        color: #333;
        text-decoration: none;
        min-width: 40px;
    }
    
    .pagination-link:hover {
        background-color: #f5f5f5;
    }
    
    .pagination-link.active {
        background-color: #3498db;
        color: white;
        border-color: #3498db;
        font-weight: 500;
    }
    
    .pagination-link.disabled {
        color: #ccc;
        pointer-events: none;
        background-color: #f9f9f9;
    }
    
    /* Tooltip */
    .tooltip {
        position: relative;
    }
    
    .tooltip-text {
        visibility: hidden;
        background-color: #333;
        color: #fff;
        text-align: center;
        border-radius: 4px;
        padding: 5px 10px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0;
        transition: opacity 0.3s;
        font-size: 12px;
        white-space: nowrap;
    }
    
    .tooltip-text::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #333 transparent transparent transparent;
    }
    
    .tooltip:hover .tooltip-text {
        visibility: visible;
        opacity: 1;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .form-buttons {
            justify-content: flex-start;
        }
        
        .action-buttons {
            flex-wrap: wrap;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Header -->
    <div class="page-header">
        <h1 class="page-title">Daftar Produk</h1>
        <a href="{% url 'dashboard:product_create' %}" class="btn-add">
            <i class="fas fa-plus"></i> Tambah Produk
        </a>
    </div>
    
    <!-- Statistik Produk -->
    <div class="stats-container">
        <div class="stat-card blue">
            <div class="stat-icon">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-info">
                <h5>Total Produk</h5>
                <h2>{{ total_products }}</h2>
            </div>
        </div>
        <div class="stat-card green">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-info">
                <h5>Stok Tersedia</h5>
                <h2>{{ available_count }}</h2>
            </div>
        </div>
        <div class="stat-card yellow">
            <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-info">
                <h5>Stok Rendah</h5>
                <h2>{{ low_stock_count }}</h2>
            </div>
        </div>
        <div class="stat-card red">
            <div class="stat-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="stat-info">
                <h5>Stok Habis</h5>
                <h2>{{ out_of_stock_count }}</h2>
            </div>
        </div>
    </div>
    
    <!-- Filter dan Pencarian -->
    <div class="filter-box">
        <form method="get" class="filter-form">
            <div class="form-group">
                <label for="{{ form.search.id_for_label }}">Cari Produk</label>
                <div class="input-icon-wrapper">
                    <i class="fas fa-search input-icon"></i>
                    <input type="text" name="{{ form.search.name }}" id="{{ form.search.id_for_label }}" 
                           value="{{ form.search.value|default:'' }}" class="form-control input-with-icon" 
                           placeholder="Nama atau kode produk">
                </div>
            </div>
            <div class="form-group">
                <label for="{{ form.category.id_for_label }}">Kategori</label>
                <div class="input-icon-wrapper">
                    <i class="fas fa-tag input-icon"></i>
                    <select name="{{ form.category.name }}" id="{{ form.category.id_for_label }}" 
                            class="form-control input-with-icon">
                        <option value="">Semua Kategori</option>
                        {% for choice in form.category.field.choices %}
                            {% if choice.0 %}
                            <option value="{{ choice.0 }}" {% if form.category.value|stringformat:"s" == choice.0|stringformat:"s" %}selected{% endif %}>
                                {{ choice.1 }}
                            </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="{{ form.stock_status.id_for_label }}">Status Stok</label>
                <div class="input-icon-wrapper">
                    <i class="fas fa-layer-group input-icon"></i>
                    <select name="{{ form.stock_status.name }}" id="{{ form.stock_status.id_for_label }}" 
                            class="form-control input-with-icon">
                        <option value="">Semua Status</option>
                        {% for choice in form.stock_status.field.choices %}
                            {% if choice.0 %}
                            <option value="{{ choice.0 }}" {% if form.stock_status.value == choice.0 %}selected{% endif %}>
                                {{ choice.1 }}
                            </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="{{ form.expiry_status.id_for_label }}">Status Kedaluwarsa</label>
                <div class="input-icon-wrapper">
                    <i class="fas fa-calendar-alt input-icon"></i>
                    <select name="{{ form.expiry_status.name }}" id="{{ form.expiry_status.id_for_label }}" 
                            class="form-control input-with-icon">
                        <option value="">Semua Status</option>
                        {% for choice in form.expiry_status.field.choices %}
                            {% if choice.0 %}
                            <option value="{{ choice.0 }}" {% if form.expiry_status.value == choice.0 %}selected{% endif %}>
                                {{ choice.1 }}
                            </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="form-buttons">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Cari
                </button>
                <a href="{% url 'dashboard:product_list' %}" class="btn btn-secondary">
                    <i class="fas fa-redo"></i> Reset
                </a>
            </div>
        </form>
    </div>
    
    <!-- Tabel Produk -->
    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Kode</th>
                        <th>Nama</th>
                        <th>Kategori</th>
                        <th>Ukuran</th>
                        <th>Warna</th>
                        <th>Harga Jual</th>
                        <th>Stok</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>{{ product.code }}</td>
                        <td><strong>{{ product.name }}</strong></td>
                        <td>{{ product.category.name|default:"-" }}</td>
                        <td>{{ product.get_size_display }}</td>
                        <td>{{ product.get_color_display }}</td>
                        <td>Rp {{ product.selling_price|floatformat:0|intcomma }}</td>
                        <td>{{ product.quantity }} {{ product.unit }}</td>
                        <td>
                            {% if product.quantity == 0 %}
                            <span class="badge badge-danger">
                                <i class="fas fa-times-circle"></i> Habis
                            </span>
                            {% elif product.is_low_stock %}
                            <span class="badge badge-warning">
                                <i class="fas fa-exclamation-triangle"></i> Rendah ({{ product.quantity }})
                            </span>
                            {% else %}
                            <span class="badge badge-success">
                                <i class="fas fa-check-circle"></i> {{ product.quantity }}
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'dashboard:product_detail' product.id %}" class="btn-icon view tooltip">
                                    <i class="fas fa-eye"></i>
                                    <span class="tooltip-text">Detail</span>
                                </a>
                                <a href="{% url 'dashboard:product_edit' product.id %}" class="btn-icon edit tooltip">
                                    <i class="fas fa-edit"></i>
                                    <span class="tooltip-text">Edit</span>
                                </a>
                                <a href="{% url 'dashboard:product_delete' product.id %}" class="btn-icon delete tooltip">
                                    <i class="fas fa-trash"></i>
                                    <span class="tooltip-text">Hapus</span>
                                </a>
                                <a href="{% url 'dashboard:stock_in' %}?product={{ product.id }}" class="btn-icon stock-in tooltip">
                                    <i class="fas fa-plus-circle"></i>
                                    <span class="tooltip-text">Tambah Stok</span>
                                </a>
                                <a href="{% url 'dashboard:stock_out' %}?product={{ product.id }}" class="btn-icon stock-out tooltip">
                                    <i class="fas fa-minus-circle"></i>
                                    <span class="tooltip-text">Kurangi Stok</span>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9">
                            <div class="empty-state">
                                <i class="fas fa-box-open"></i>
                                <p>Tidak ada produk yang ditemukan.</p>
                                <a href="{% url 'dashboard:product_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Tambah Produk Baru
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    {% if products.has_other_pages %}
    <div class="pagination">
        <ul class="pagination-list">
            {% if products.has_previous %}
            <li class="pagination-item">
                <a href="?page={{ products.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                   class="pagination-link">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            {% else %}
            <li class="pagination-item">
                <span class="pagination-link disabled">
                    <i class="fas fa-chevron-left"></i>
                </span>
            </li>
            {% endif %}
            
            {% for i in products.paginator.page_range %}
                {% if products.number == i %}
                <li class="pagination-item">
                    <span class="pagination-link active">{{ i }}</span>
                </li>
                {% elif i > products.number|add:'-3' and i < products.number|add:'3' %}
                <li class="pagination-item">
                    <a href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                       class="pagination-link">
                        {{ i }}
                    </a>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if products.has_next %}
            <li class="pagination-item">
                <a href="?page={{ products.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                   class="pagination-link">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% else %}
            <li class="pagination-item">
                <span class="pagination-link disabled">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Animasi untuk card statistik
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 15px rgba(0, 0, 0, 0.2)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        });
        
        // Highlight baris tabel saat hover
        const tableRows = document.querySelectorAll('.data-table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f5f5f5';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
        
        // Auto-submit form saat select berubah
        const selectElements = document.querySelectorAll('select[name="period"], select[name="category"], select[name="stock_status"], select[name="expiry_status"]');
        selectElements.forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });
    });
</script>
{% endblock %}
{% endblock %}





