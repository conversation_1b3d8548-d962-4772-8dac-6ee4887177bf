from django.conf import settings
from .utils import get_user_role

def debug(request):
    """
    Menambahkan variabel debug ke context
    """
    return {'debug': settings.DEBUG}

def user_role(request):
    """
    Menambahkan role user ke context
    """
    if request.user.is_authenticated:
        return {
            'user_role': get_user_role(request.user),
            'is_admin': request.user.is_staff and not request.user.is_superuser,
            'is_owner': request.user.is_superuser,
            'is_admin_or_owner': request.user.is_staff or request.user.is_superuser
        }
    return {
        'user_role': 'guest',
        'is_admin': False,
        'is_owner': False,
        'is_admin_or_owner': False
    }