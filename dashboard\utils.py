from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.db.models import F, Q
from .models import Product, Notification
from django.utils import timezone
from datetime import timed<PERSON>ta
from django.contrib.auth.decorators import user_passes_test
from django.core.exceptions import PermissionDenied
from django.contrib.auth.models import User

def get_user_role(user):
    """
    Mendapatkan role user berdasarkan permissions
    """
    if user.is_superuser:
        return 'owner'
    elif user.is_staff:
        return 'admin'
    else:
        return 'user'

def is_admin_or_owner(user):
    """
    Mengecek apakah user adalah admin atau owner
    """
    return user.is_authenticated and (user.is_staff or user.is_superuser)

def is_owner(user):
    """
    Mengecek apakah user adalah owner (superuser)
    """
    return user.is_authenticated and user.is_superuser

def require_admin_or_owner(view_func):
    """
    Decorator untuk view yang hanya bisa diakses admin atau owner
    """
    return user_passes_test(is_admin_or_owner)(view_func)

def require_owner(view_func):
    """
    Decorator untuk view yang hanya bisa diakses owner
    """
    return user_passes_test(is_owner)(view_func)

def get_admin_and_owner_emails():
    """
    Mendapatkan daftar email admin dan owner yang terdaftar
    """
    # Cari user dengan role admin (is_staff=True, is_superuser=False) dan owner (is_superuser=True)
    admin_and_owner_users = User.objects.filter(
        Q(is_staff=True) | Q(is_superuser=True),
        email__isnull=False,
        email__gt='',  # Email tidak kosong
        is_active=True  # User masih aktif
    ).distinct()

    # Ambil email yang valid
    emails = [user.email for user in admin_and_owner_users if user.email]
    return emails

def send_low_stock_email_notification(product=None):
    """
    Mengirim email notifikasi untuk produk dengan stok rendah atau habis
    Jika product diberikan, kirim notifikasi untuk produk tersebut saja
    Jika tidak, kirim notifikasi untuk semua produk yang stok rendah/habis
    """
    if product:
        # Notifikasi untuk produk tertentu
        if product.is_low_stock:
            low_stock_products = [product]
            out_of_stock_products = []
        elif product.is_out_of_stock:
            low_stock_products = []
            out_of_stock_products = [product]
        else:
            # Produk tidak memerlukan notifikasi
            return
    else:
        # Notifikasi untuk semua produk
        low_stock_products = Product.objects.filter(quantity__lte=F('min_stock_level'), quantity__gt=0)
        out_of_stock_products = Product.objects.filter(quantity=0)

        # Jika tidak ada produk yang perlu dinotifikasi, tidak perlu mengirim email
        if not low_stock_products.exists() and not out_of_stock_products.exists():
            return

    # Dapatkan email admin dan owner
    recipient_emails = get_admin_and_owner_emails()

    if not recipient_emails:
        # Tidak ada email admin/owner yang valid
        return

    # Render template email
    html_message = render_to_string('dashboard/emails/low_stock_notification.html', {
        'low_stock_products': low_stock_products,
        'out_of_stock_products': out_of_stock_products,
    })

    # Versi plain text dari email
    plain_message = strip_tags(html_message)

    # Tentukan subject berdasarkan jenis notifikasi
    if product:
        if product.is_low_stock:
            subject = f'[Stock Manager] Stok Rendah - {product.name}'
        else:
            subject = f'[Stock Manager] Stok Habis - {product.name}'
    else:
        subject = '[Stock Manager] Notifikasi Stok Rendah'

    try:
        # Kirim email ke semua admin dan owner
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=recipient_emails,
            html_message=html_message,
            fail_silently=False,
        )
    except Exception as e:
        # Log error jika diperlukan
        print(f"Error sending email notification: {e}")

def send_stock_notification_email(notification):
    """
    Mengirim email notifikasi untuk notifikasi stok tertentu
    """
    if notification.type not in ['low_stock', 'out_of_stock']:
        # Hanya kirim email untuk notifikasi stok rendah dan habis
        return

    # Kirim email untuk produk tertentu
    send_low_stock_email_notification(product=notification.product)

def check_expiring_products():
    """
    Memeriksa produk yang akan kedaluwarsa dalam 30 hari dan membuat notifikasi
    """
    today = timezone.now().date()
    expiry_threshold = today + timedelta(days=30)
    
    # Cari produk yang akan kedaluwarsa dalam 30 hari
    expiring_products = Product.objects.filter(
        expiry_date__isnull=False,
        expiry_date__gt=today,
        expiry_date__lte=expiry_threshold
    )
    
    for product in expiring_products:
        days_left = (product.expiry_date - today).days
        
        # Cek apakah sudah ada notifikasi yang belum dibaca untuk produk ini
        existing_notification = Notification.objects.filter(
            product=product,
            type='restock_reminder',
            status__in=['unread', 'read'],
            message__contains=f"akan kedaluwarsa dalam {days_left} hari"
        ).exists()
        
        if not existing_notification:
            # Buat notifikasi baru
            Notification.objects.create(
                product=product,
                type='restock_reminder',
                message=f"Produk {product.name} akan kedaluwarsa dalam {days_left} hari (pada {product.expiry_date.strftime('%d/%m/%Y')})."
            )
    
    # Cari produk yang sudah kedaluwarsa
    expired_products = Product.objects.filter(
        expiry_date__isnull=False,
        expiry_date__lte=today
    )
    
    for product in expired_products:
        # Cek apakah sudah ada notifikasi yang belum dibaca untuk produk ini
        existing_notification = Notification.objects.filter(
            product=product,
            type='restock_reminder',
            status__in=['unread', 'read'],
            message__contains="telah kedaluwarsa"
        ).exists()
        
        if not existing_notification:
            # Buat notifikasi baru
            Notification.objects.create(
                product=product,
                type='restock_reminder',
                message=f"Produk {product.name} telah kedaluwarsa pada {product.expiry_date.strftime('%d/%m/%Y')}. Harap segera ditindaklanjuti."
            )


