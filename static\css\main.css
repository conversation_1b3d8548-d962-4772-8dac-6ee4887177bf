/* Reset dan dasar */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.flex {
  display: flex;
}

.h-screen {
  height: 100vh;
}

.overflow-hidden {
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: 250px;
  background-color: #2c3e50;
  color: #ecf0f1;
  height: 100%;
  overflow-y: auto;
}

.navbar-brand {
  padding: 15px;
  font-size: 1.2rem;
  font-weight: bold;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-sticky {
  padding-top: 10px;
}

.nav {
  list-style: none;
}

.nav-item {
  margin-bottom: 5px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: #ecf0f1;
  text-decoration: none;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  background-color: #3498db;
  color: white;
}

.nav-link i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.sidebar-heading {
  padding: 10px 15px;
  font-size: 0.8rem;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 10px;
}

/* Content */
.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

/* Cards */
.card {
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: 15px;
}

.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
}

tr:hover {
  background-color: #f8f9fa;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 8px 15px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
}

.btn-success {
  background-color: #2ecc71;
  color: white;
}

.btn-success:hover {
  background-color: #27ae60;
}

.btn-danger {
  background-color: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 0.875rem;
}

/* Forms */
.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

/* Alerts */
.alert {
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Badges */
.badge {
  display: inline-block;
  padding: 3px 7px;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 10px;
}

.badge-primary {
  background-color: #3498db;
  color: white;
}

.badge-success {
  background-color: #2ecc71;
  color: white;
}

.badge-warning {
  background-color: #f39c12;
  color: white;
}

.badge-danger {
  background-color: #e74c3c;
  color: white;
}

/* Utilities */
.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.d-flex {
  display: flex;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}

.ml-3 {
  margin-left: 0.75rem;
}

.row {
  margin-bottom: 20px;
}

/* Dark mode */
.dark-mode {
  background-color: #1a1a1a;
  color: #f5f5f5;
}

.dark-mode .sidebar {
  background-color: #2d2d2d;
}

.dark-mode .card {
  background-color: #2d2d2d;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.dark-mode .card-header {
  border-bottom: 1px solid #3d3d3d;
}

.dark-mode th, .dark-mode td {
  border-bottom: 1px solid #3d3d3d;
}

.dark-mode th {
  background-color: #333;
}

.dark-mode tr:hover {
  background-color: #333;
}

.dark-mode .form-control {
  background-color: #333;
  border-color: #444;
  color: #f5f5f5;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 60px;
  }
  
  .sidebar .navbar-brand span,
  .sidebar .nav-link span {
    display: none;
  }
  
  .sidebar .nav-link i {
    margin-right: 0;
  }
  
  .content {
    margin-left: 60px;
  }
  
  .card-container {
    grid-template-columns: 1fr;
  }
}

