{% extends "dashboard/base.html" %}

{% block title %}La<PERSON><PERSON> & Keluar{% endblock %}

{% block head_extras %}
<style>
    /* Styling untuk laporan transaksi */
    .report-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .report-header {
        margin-bottom: 20px;
    }
    
    .report-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .report-subtitle {
        font-size: 16px;
        color: #6c757d;
        margin-bottom: 20px;
    }
    
    /* Filter box styling */
    .filter-box {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .filter-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        font-size: 14px;
        color: #555;
    }
    
    .input-icon-wrapper {
        position: relative;
    }
    
    .input-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }
    
    .input-with-icon {
        padding-left: 35px;
    }
    
    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s, box-shadow 0.3s;
    }
    
    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
        outline: none;
    }
    
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.5;
        border-radius: 4px;
        transition: color 0.15s, background-color 0.15s, border-color 0.15s, box-shadow 0.15s;
        cursor: pointer;
    }
    
    .btn-primary {
        color: #fff;
        background-color: #3498db;
        border-color: #3498db;
    }
    
    .btn-primary:hover {
        background-color: #2980b9;
        border-color: #2980b9;
    }
    
    .chart-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .chart-header {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .chart-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }
    
    .chart-body {
        padding: 20px;
        position: relative;
        min-height: 300px;
    }
    
    .table-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .table-header {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .table-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }
    
    .table-body {
        padding: 0;
    }
    
    .table {
        margin-bottom: 0;
    }
    
    .table th {
        font-weight: 600;
        background-color: #f8f9fa;
    }
    
    .badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
    }
    
    .btn-export {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-export:hover {
        background-color: #218838;
    }
    
    .summary-cards {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .summary-card {
        flex: 1;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        padding: 20px;
        display: flex;
        align-items: center;
    }
    
    .summary-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
    }
    
    .summary-icon.in {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }
    
    .summary-icon.out {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .summary-icon.total {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .summary-content {
        flex: 1;
    }
    
    .summary-title {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 5px;
    }
    
    .summary-value {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="report-container">
        <div class="report-header">
            <h1 class="report-title">Laporan Barang Masuk & Keluar</h1>
            <p class="report-subtitle">Analisis pergerakan barang masuk dan keluar</p>
        </div>
        
        <!-- Filter -->
        <div class="filter-box">
            <form method="get" class="filter-form">
                <div class="form-group">
                    <label for="period" class="form-label">Periode</label>
                    <div class="input-icon-wrapper">
                        <i class="fas fa-calendar input-icon"></i>
                        <select name="period" id="period" class="form-control input-with-icon">
                            <option value="daily" {% if period == 'daily' %}selected{% endif %}>Harian (7 hari terakhir)</option>
                            <option value="weekly" {% if period == 'weekly' %}selected{% endif %}>Mingguan</option>
                            <option value="monthly" {% if period == 'monthly' %}selected{% endif %}>Bulanan</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="product" class="form-label">Produk</label>
                    <div class="input-icon-wrapper">
                        <i class="fas fa-box input-icon"></i>
                        <select name="product" id="product" class="form-control input-with-icon">
                            <option value="">Semua Produk</option>
                            {% for p in all_products %}
                            <option value="{{ p.id }}" {% if product_id == p.id %}selected{% endif %}>{{ p.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="movement_type" class="form-label">Tipe Pergerakan</label>
                    <div class="input-icon-wrapper">
                        <i class="fas fa-exchange-alt input-icon"></i>
                        <select name="movement_type" id="movement_type" class="form-control input-with-icon">
                            <option value="">Semua</option>
                            <option value="IN" {% if movement_type == 'IN' %}selected{% endif %}>Barang Masuk</option>
                            <option value="OUT" {% if movement_type == 'OUT' %}selected{% endif %}>Barang Keluar</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Ringkasan -->
        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-icon in">
                    <i class="fas fa-arrow-circle-down"></i>
                </div>
                <div class="summary-content">
                    <div class="summary-title">Total Barang Masuk</div>
                    <div class="summary-value">{{ total_in }}</div>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon out">
                    <i class="fas fa-arrow-circle-up"></i>
                </div>
                <div class="summary-content">
                    <div class="summary-title">Total Barang Keluar</div>
                    <div class="summary-value">{{ total_out }}</div>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon total">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="summary-content">
                    <div class="summary-title">Total Transaksi</div>
                    <div class="summary-value">{{ total_transactions }}</div>
                </div>
            </div>
        </div>
        
        <!-- Grafik Pergerakan -->
        <div class="chart-card">
            <div class="chart-header">
                <h5 class="chart-title">Grafik Pergerakan Barang</h5>
                <div class="chart-actions">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleChartType()">
                        <i class="fas fa-chart-line"></i> Ubah Tipe Grafik
                    </button>
                    <!-- Tombol export PDF dan Excel dihapus -->
                </div>
            </div>
            <div class="chart-body">
                <canvas id="transactionChart" height="300"></canvas>
            </div>
        </div>
        
        <!-- Tabel Transaksi -->
        <div class="table-card">
            <div class="table-header">
                <h5 class="table-title">Daftar Transaksi</h5>
            </div>
            <div class="table-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Tanggal</th>
                            <th>Produk</th>
                            <th>Tipe</th>
                            <th>Jumlah</th>
                            <th>Catatan</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in movements %}
                        <tr>
                            <td>{{ movement.timestamp|date:"d M Y H:i" }}</td>
                            <td>
                                <a href="{% url 'dashboard:product_detail' movement.product.id %}">
                                    {{ movement.product.name }}
                                </a>
                            </td>
                            <td>
                                {% if movement.movement_type == 'IN' %}
                                <span class="badge badge-success">Masuk</span>
                                {% else %}
                                <span class="badge badge-danger">Keluar</span>
                                {% endif %}
                            </td>
                            <td>{{ movement.quantity }}</td>
                            <td>{{ movement.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">Tidak ada data transaksi</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let transactionChart;
    let currentChartType = 'line'; // Changed from 'bar' to 'line'
    
    document.addEventListener('DOMContentLoaded', function() {
        // Data untuk grafik
        const periods = {{ periods_json|safe }};
        const inData = {{ in_data_json|safe }};
        const outData = {{ out_data_json|safe }};
        
        // Pastikan elemen canvas ada
        const chartElement = document.getElementById('transactionChart');
        if (chartElement) {
            const ctx = chartElement.getContext('2d');
            
            // Buat grafik
            transactionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: periods,
                    datasets: [
                        {
                            label: 'Barang Masuk',
                            data: inData,
                            backgroundColor: 'rgba(40, 167, 69, 0.5)',
                            borderColor: 'rgba(40, 167, 69, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: 'rgba(40, 167, 69, 1)',
                            pointRadius: 4,
                            pointHoverRadius: 6
                        },
                        {
                            label: 'Barang Keluar',
                            data: outData,
                            backgroundColor: 'rgba(220, 53, 69, 0.5)',
                            borderColor: 'rgba(220, 53, 69, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: 'rgba(220, 53, 69, 1)',
                            pointRadius: 4,
                            pointHoverRadius: 6
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        }
    });
    
    // Fungsi untuk mengubah tipe grafik
    function toggleChartType() {
        if (!transactionChart) return;
        
        currentChartType = currentChartType === 'line' ? 'bar' : 'line'; // Changed order
        transactionChart.config.type = currentChartType;
        transactionChart.update();
    }
    
    // Fungsi untuk export ke PDF
    function exportToPDF() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('landscape');
        
        // Judul laporan
        doc.setFontSize(18);
        doc.text('Laporan Barang Masuk & Keluar', 14, 22);
        
        // Periode dan filter
        const periodSelect = document.getElementById('period');
        const periodText = periodSelect.options[periodSelect.selectedIndex].text;
        
        const productSelect = document.getElementById('product');
        const productText = productSelect.selectedIndex > 0 ? 
            productSelect.options[productSelect.selectedIndex].text : 'Semua Produk';
        
        const typeSelect = document.getElementById('movement_type');
        const typeText = typeSelect.selectedIndex > 0 ? 
            typeSelect.options[typeSelect.selectedIndex].text : 'Semua Tipe';
        
        doc.setFontSize(12);
        doc.text(`Periode: ${periodText}`, 14, 30);
        doc.text(`Produk: ${productText}`, 14, 38);
        doc.text(`Tipe: ${typeText}`, 14, 46);
        
        // Tambahkan tanggal laporan
        const today = new Date();
        doc.text(`Tanggal Laporan: ${today.toLocaleDateString('id-ID')}`, 14, 54);
        
        // Ringkasan
        doc.setFontSize(14);
        doc.text('Ringkasan', 14, 65);
        
        const summaryData = [
            ['Total Barang Masuk', 'Total Barang Keluar', 'Total Transaksi'],
            [
                document.querySelector('.summary-cards .summary-card:nth-child(1) .summary-value').textContent,
                document.querySelector('.summary-cards .summary-card:nth-child(2) .summary-value').textContent,
                document.querySelector('.summary-cards .summary-card:nth-child(3) .summary-value').textContent
            ]
        ];
        
        doc.autoTable({
            startY: 70,
            head: [summaryData[0]],
            body: [summaryData[1]],
            theme: 'grid',
            headStyles: { fillColor: [66, 66, 66] },
            margin: { left: 14, right: 14 }
        });
        
        // Ambil data dari tabel transaksi
        const transactionTable = document.querySelector('.table-card table');
        if (transactionTable) {
            const finalY = doc.lastAutoTable.finalY + 15;
            
            doc.setFontSize(14);
            doc.text('Daftar Transaksi', 14, finalY);
            
            doc.autoTable({
                html: transactionTable,
                startY: finalY + 5,
                theme: 'grid',
                headStyles: { fillColor: [66, 66, 66] },
                margin: { left: 14, right: 14 }
            });
        }
        
        // Tambahkan grafik
        if (transactionChart) {
            const finalY = doc.lastAutoTable.finalY + 15;
            doc.setFontSize(14);
            doc.text('Grafik Pergerakan Barang', 14, finalY);
            
            // Konversi grafik ke gambar
            const chartImage = transactionChart.toBase64Image();
            doc.addImage(chartImage, 'PNG', 14, finalY + 5, 270, 120);
        }
        
        // Simpan PDF
        doc.save('laporan-transaksi.pdf');
    }
    
    // Fungsi untuk export ke Excel
    function exportToExcel() {
        // Buat workbook baru
        const wb = XLSX.utils.book_new();
        
        // Ringkasan
        const summaryData = [
            ['Total Barang Masuk', 'Total Barang Keluar', 'Total Transaksi'],
            [
                document.querySelector('.summary-cards .summary-card:nth-child(1) .summary-value').textContent,
                document.querySelector('.summary-cards .summary-card:nth-child(2) .summary-value').textContent,
                document.querySelector('.summary-cards .summary-card:nth-child(3) .summary-value').textContent
            ]
        ];
        
        const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(wb, summaryWs, "Ringkasan");
        
        // Ambil data dari tabel transaksi
        const transactionTable = document.querySelector('.table-card table');
        if (transactionTable) {
            const transactionData = tableToArray(transactionTable);
            const transactionWs = XLSX.utils.aoa_to_sheet(transactionData);
            XLSX.utils.book_append_sheet(wb, transactionWs, "Transaksi");
        }
        
        // Buat sheet untuk data grafik
        if (transactionChart) {
            const chartData = [
                ['Periode', 'Barang Masuk', 'Barang Keluar']
            ];
            
            const labels = transactionChart.data.labels;
            const inData = transactionChart.data.datasets[0].data;
            const outData = transactionChart.data.datasets[1].data;
            
            for (let i = 0; i < labels.length; i++) {
                chartData.push([labels[i], inData[i], outData[i]]);
            }
            
            const chartWs = XLSX.utils.aoa_to_sheet(chartData);
            XLSX.utils.book_append_sheet(wb, chartWs, "Data Grafik");
        }
        
        // Simpan file Excel
        XLSX.writeFile(wb, "laporan-transaksi.xlsx");
    }
    
    // Fungsi untuk mengkonversi tabel HTML ke array
    function tableToArray(table) {
        const rows = table.querySelectorAll('tr');
        const data = [];
        
        rows.forEach(row => {
            const rowData = [];
            const cells = row.querySelectorAll('th, td');
            
            cells.forEach(cell => {
                // Hapus link dan ambil teks saja
                rowData.push(cell.textContent.trim());
            });
            
            data.push(rowData);
        });
        
        return data;
    }
</script>
{% endblock %}


