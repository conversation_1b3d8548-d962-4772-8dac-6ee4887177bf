/* Badge styling yang konsisten */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
    line-height: 1;
    gap: 5px;
}

.badge i {
    font-size: 10px;
}

.badge-success {
    background-color: #2ecc71;
    color: white;
}

.badge-warning {
    background-color: #f39c12;
    color: #fff;
}

.badge-danger {
    background-color: #e74c3c;
    color: white;
}

.badge-info {
    background-color: #3498db;
    color: white;
}

.badge-secondary {
    background-color: #95a5a6;
    color: white;
}

/* Stat card styling */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-card.blue {
    border-left: 4px solid #3498db;
}

.stat-card.green {
    border-left: 4px solid #2ecc71;
}

.stat-card.yellow {
    border-left: 4px solid #f39c12;
}

.stat-card.red {
    border-left: 4px solid #e74c3c;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
}

.stat-card.blue .stat-icon {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.stat-card.green .stat-icon {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
}

.stat-card.yellow .stat-icon {
    background-color: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.stat-card.red .stat-icon {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.stat-info h5 {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0 0 5px;
}

.stat-info h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: #2c3e50;
}

/* Action buttons untuk notifikasi */
.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-icon {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 12px;
    transition: background-color 0.3s;
    position: relative;
}

.btn-icon.view {
    background-color: #3498db;
}

.btn-icon.view:hover {
    background-color: #2980b9;
}

.btn-icon.delete {
    background-color: #e74c3c;
}

.btn-icon.delete:hover {
    background-color: #c0392b;
}

/* Tooltip styling */
.tooltip {
    position: relative;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Empty state styling */
.empty-state {
    padding: 40px 20px;
    text-align: center;
}

.empty-state i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.empty-state p {
    color: #777;
    margin-bottom: 15px;
}

/* Styling untuk dropdown notifikasi */
/* Styling untuk navbar yang masih diperlukan */
.navbar {
    padding: 0.5rem 1rem;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.navbar-nav {
    display: flex;
    align-items: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    padding: 0.5rem 1rem;
    color: #333;
    position: relative;
}

.nav-link i {
    font-size: 18px;
}

/* Perbaikan untuk dropdown menu */
.dropdown-menu {
    padding: 0;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

/* Perbaikan untuk dropdown toggle */
.dropdown-toggle::after {
    display: none;
}

/* Perbaikan untuk badge notifikasi */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.nav-link .badge {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 10px;
    padding: 3px 6px;
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
    transform: translate(25%, -25%);
}

/* Responsif untuk dropdown notifikasi */
@media (max-width: 768px) {
    .notification-dropdown {
        width: 290px;
    }
    
    .notification-text {
        font-size: 12px;
    }
    
    .notification-icon {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 576px) {
    .notification-dropdown {
        width: 100%;
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        margin-top: 0;
        border-radius: 0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .notification-item {
        padding: 10px 15px;
    }
    
    .notification-icon {
        width: 30px;
        height: 30px;
        margin-right: 10px;
    }
}

/* Animasi untuk dropdown */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.notification-dropdown {
    animation: fadeIn 0.2s ease-out;
}

/* Styling untuk hover pada notifikasi */
.notification-item:hover .notification-text {
    color: #2980b9;
}

/* Styling untuk scrollbar di dropdown */
.notification-list::-webkit-scrollbar {
    width: 6px;
}

.notification-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.notification-list::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
    background: #ccc;
}

/* Efek hover dan fokus untuk link notifikasi */
.nav-link:hover, .nav-link:focus {
    color: #3498db;
}

.nav-link:hover i, .nav-link:focus i {
    transform: scale(1.1);
    transition: transform 0.2s;
}

/* Efek hover untuk item notifikasi */
.notification-item {
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: #f0f7ff;
}

/* Styling untuk notifikasi yang belum dibaca */
.notification-item.unread {
    background-color: #f8f9fa;
}

.notification-item.unread:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #3498db;
}

/* Styling untuk tombol aksi notifikasi */
.btn-group {
    display: flex;
    gap: 10px;
}

.btn-group .btn {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-group .btn i {
    margin-right: 5px;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-danger {
    background-color: #e74c3c;
    border-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}


