from django.core.management.base import BaseCommand
from dashboard.utils import get_admin_and_owner_emails, send_low_stock_email_notification
from dashboard.models import Product
from django.contrib.auth.models import User

class Command(BaseCommand):
    help = 'Menguji sistem email notifikasi stok rendah'

    def add_arguments(self, parser):
        parser.add_argument(
            '--check-emails',
            action='store_true',
            help='Hanya cek email admin dan owner yang terdaftar',
        )
        parser.add_argument(
            '--send-test',
            action='store_true',
            help='Kirim email test dengan data dummy',
        )

    def handle(self, *args, **options):
        if options['check_emails']:
            self.check_admin_emails()
        elif options['send_test']:
            self.send_test_email()
        else:
            self.stdout.write('Gunakan --check-emails untuk cek email atau --send-test untuk kirim test email')

    def check_admin_emails(self):
        self.stdout.write('Memeriksa email admin dan owner...')
        
        # <PERSON><PERSON><PERSON>an semua user admin dan owner
        admin_users = User.objects.filter(is_staff=True, is_superuser=False)
        owner_users = User.objects.filter(is_superuser=True)
        
        self.stdout.write(f'\nAdmin users ({admin_users.count()}):')
        for user in admin_users:
            email_status = user.email if user.email else 'TIDAK ADA EMAIL'
            active_status = 'AKTIF' if user.is_active else 'TIDAK AKTIF'
            self.stdout.write(f'  - {user.username}: {email_status} ({active_status})')
        
        self.stdout.write(f'\nOwner users ({owner_users.count()}):')
        for user in owner_users:
            email_status = user.email if user.email else 'TIDAK ADA EMAIL'
            active_status = 'AKTIF' if user.is_active else 'TIDAK AKTIF'
            self.stdout.write(f'  - {user.username}: {email_status} ({active_status})')
        
        # Tampilkan email yang akan menerima notifikasi
        recipient_emails = get_admin_and_owner_emails()
        self.stdout.write(f'\nEmail yang akan menerima notifikasi ({len(recipient_emails)}):')
        for email in recipient_emails:
            self.stdout.write(f'  - {email}')
        
        if not recipient_emails:
            self.stdout.write(
                self.style.WARNING('\nPERINGATAN: Tidak ada email yang valid untuk menerima notifikasi!')
            )
            self.stdout.write('Pastikan admin/owner memiliki email yang valid dan akun aktif.')

    def send_test_email(self):
        self.stdout.write('Mengirim email test...')
        
        # Cek apakah ada email penerima
        recipient_emails = get_admin_and_owner_emails()
        if not recipient_emails:
            self.stdout.write(
                self.style.ERROR('Tidak dapat mengirim email test: tidak ada email penerima yang valid.')
            )
            return
        
        # Cari produk untuk test, atau buat data dummy
        test_products = Product.objects.all()[:2]
        
        if not test_products.exists():
            self.stdout.write(
                self.style.WARNING('Tidak ada produk untuk test. Pastikan ada data produk di database.')
            )
            return
        
        # Kirim email test dengan produk pertama
        first_product = test_products.first()
        self.stdout.write(f'Mengirim email test untuk produk: {first_product.name}')
        
        try:
            send_low_stock_email_notification(product=first_product)
            self.stdout.write(
                self.style.SUCCESS(f'Email test berhasil dikirim ke {len(recipient_emails)} penerima.')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Gagal mengirim email test: {e}')
            )
