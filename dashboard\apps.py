from django.apps import AppConfig


class DashboardConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'dashboard'

    def ready(self):
        # Import untuk menghindari circular import
        from django.db import connection
        from django.db.utils import OperationalError
        
        # Cek apakah tabel kategori sudah ada
        try:
            # Coba buat kategori dan satuan default jika tabel sudah ada
            self.create_default_categories()
            self.create_default_units()
        except OperationalError:
            # Tabel belum ada, mungkin migrasi belum dijalankan
            pass
    
    def create_default_categories(self):
        from .models import Category
        
        # Daftar kategori default
        default_categories = [
            {'name': 'Tas', 'description': '<PERSON>rbaga<PERSON> jenis tas'},
            {'name': 'Baju', 'description': 'Berbagai jenis pakaian'},
            {'name': 'Aksesoris', 'description': '<PERSON>rb<PERSON><PERSON> jenis aksesoris'},
        ]
        
        # Buat kategori jika belum ada
        for category_data in default_categories:
            Category.objects.get_or_create(
                name=category_data['name'],
                defaults={'description': category_data['description']}
            )

    def create_default_units(self):
        from .models import UnitOfMeasure
        
        # Daftar satuan default
        default_units = [
            {'name': 'Pieces', 'abbreviation': 'pcs'},
            {'name': 'Lusin', 'abbreviation': 'lsn'},
            {'name': 'Kilogram', 'abbreviation': 'kg'},
            {'name': 'Gram', 'abbreviation': 'g'},
            {'name': 'Liter', 'abbreviation': 'L'},
            {'name': 'Mililiter', 'abbreviation': 'mL'},
            {'name': 'Box', 'abbreviation': 'box'},
            {'name': 'Pack', 'abbreviation': 'pack'},
        ]
        
        # Buat satuan jika belum ada
        for unit_data in default_units:
            UnitOfMeasure.objects.get_or_create(
                name=unit_data['name'],
                defaults={'abbreviation': unit_data['abbreviation']}
            )
