{% extends "dashboard/base.html" %}
{% block title %}Notifikasi{% endblock %}
{% block header_title %}Notifikasi{% endblock %}

{% block head_extras %}
<style>
    /* Styling untuk halaman notifikasi */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
    }
    
    /* Statistik cards */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        padding: 20px;
        display: flex;
        align-items: center;
        transition: transform 0.3s, box-shadow 0.3s;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .stat-card.blue {
        border-left: 4px solid #3498db;
    }
    
    .stat-card.green {
        border-left: 4px solid #2ecc71;
    }
    
    .stat-card.yellow {
        border-left: 4px solid #f39c12;
    }
    
    .stat-card.red {
        border-left: 4px solid #e74c3c;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
    }
    
    .stat-card.blue .stat-icon {
        background-color: rgba(52, 152, 219, 0.1);
        color: #3498db;
    }
    
    .stat-card.green .stat-icon {
        background-color: rgba(46, 204, 113, 0.1);
        color: #2ecc71;
    }
    
    .stat-card.yellow .stat-icon {
        background-color: rgba(243, 156, 18, 0.1);
        color: #f39c12;
    }
    
    .stat-card.red .stat-icon {
        background-color: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }
    
    .stat-info h5 {
        font-size: 14px;
        color: #7f8c8d;
        margin: 0 0 5px;
    }
    
    .stat-info h2 {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
        color: #2c3e50;
    }
    
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }
    
    .pagination .page-item {
        margin: 0 3px;
    }
    
    .pagination .page-link {
        border-radius: 4px;
        color: #3498db;
        border: 1px solid #e0e0e0;
    }
    
    .pagination .page-item.active .page-link {
        background-color: #3498db;
        border-color: #3498db;
    }
    
    .pagination .page-link:hover {
        background-color: #f8f9fa;
    }
    
    /* Status badges */
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
        line-height: 1;
        gap: 5px;
    }
    
    .badge i {
        font-size: 10px;
    }
    
    .badge-success {
        background-color: #2ecc71;
        color: white;
    }
    
    .badge-warning {
        background-color: #f39c12;
        color: #fff;
    }
    
    .badge-danger {
        background-color: #e74c3c;
        color: white;
    }
    
    .badge-info {
        background-color: #3498db;
        color: white;
    }
    
    .badge-secondary {
        background-color: #95a5a6;
        color: white;
    }
    
    /* Action buttons */
    .action-buttons {
        display: flex;
        gap: 5px;
    }
    
    .btn-icon {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 12px;
        transition: background-color 0.3s;
        position: relative;
    }
    
    .btn-icon.view {
        background-color: #3498db;
    }
    
    .btn-icon.view:hover {
        background-color: #2980b9;
    }
    
    .btn-icon.delete {
        background-color: #e74c3c;
    }
    
    .btn-icon.delete:hover {
        background-color: #c0392b;
    }
    
    /* Tooltip styling */
    .tooltip {
        position: relative;
    }
    
    .tooltip .tooltip-text {
        visibility: hidden;
        width: 120px;
        background-color: #333;
        color: #fff;
        text-align: center;
        border-radius: 4px;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        margin-left: -60px;
        opacity: 0;
        transition: opacity 0.3s;
        font-size: 12px;
    }
    
    .tooltip .tooltip-text::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #333 transparent transparent transparent;
    }
    
    .tooltip:hover .tooltip-text {
        visibility: visible;
        opacity: 1;
    }
    
    /* Empty state styling */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
    }
    
    .empty-state i {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 15px;
    }
    
    .empty-state p {
        color: #777;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fungsi untuk konfirmasi sebelum mengabaikan notifikasi
        const dismissButtons = document.querySelectorAll('.btn-icon.delete');
        dismissButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Apakah Anda yakin ingin mengabaikan notifikasi ini?')) {
                    e.preventDefault();
                }
            });
        });
        
        // Highlight baris notifikasi yang belum dibaca
        const unreadRows = document.querySelectorAll('tr:has(.badge-warning)');
        unreadRows.forEach(row => {
            row.classList.add('table-light');
        });
        
        // Animasi untuk card statistik
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.08)';
            });
        });
    });
</script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="page-header">
        <h1 class="page-title">Notifikasi</h1>
        <div>
            <span class="badge badge-info">Total: {{ total_notifications }}</span>
            <span class="badge badge-warning">Belum Dibaca: {{ unread_count }}</span>
            <span class="badge badge-success">Sudah Dibaca: {{ read_count }}</span>
            <span class="badge badge-secondary">Diabaikan: {{ dismissed_count }}</span>
            
            {% if debug %}
            <a href="{% url 'dashboard:create_test_notifications' %}" class="btn btn-sm btn-secondary ms-2">
                <i class="fas fa-plus"></i> Buat Notifikasi Test
            </a>
            {% endif %}
        </div>
    </div>
    
    <!-- Statistik Notifikasi -->
    <div class="stats-container">
        <div class="stat-card yellow">
            <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-info">
                <h5>Stok Rendah</h5>
                <h2>{{ low_stock_count }}</h2>
            </div>
        </div>
        <div class="stat-card red">
            <div class="stat-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="stat-info">
                <h5>Stok Habis</h5>
                <h2>{{ out_of_stock_count }}</h2>
            </div>
        </div>
        <div class="stat-card blue">
            <div class="stat-icon">
                <i class="fas fa-sync-alt"></i>
            </div>
            <div class="stat-info">
                <h5>Pengingat Restock</h5>
                <h2>{{ restock_reminder_count }}</h2>
            </div>
        </div>
    </div>
    
    <!-- Tambahkan setelah stats-container dan sebelum card filter -->
    <div class="mb-4">
        <div class="btn-group">
            <a href="{% url 'dashboard:notification_mark_all_read' %}" class="btn btn-primary">
                <i class="fas fa-check-double me-1"></i> Tandai Semua Dibaca
            </a>
            <a href="{% url 'dashboard:notification_dismiss_all' %}" class="btn btn-danger" onclick="return confirm('Apakah Anda yakin ingin mengabaikan semua notifikasi?')">
                <i class="fas fa-times-circle me-1"></i> Abaikan Semua
            </a>
        </div>
    </div>
    
    <!-- Filter Notifikasi -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Filter Notifikasi</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">Tipe Notifikasi</label>
                    <select name="type" class="form-select" onchange="this.form.submit()">
                        <option value="">Semua Tipe</option>
                        <option value="low_stock" {% if selected_type == 'low_stock' %}selected{% endif %}>Stok Rendah</option>
                        <option value="out_of_stock" {% if selected_type == 'out_of_stock' %}selected{% endif %}>Stok Habis</option>
                        <option value="restock_reminder" {% if selected_type == 'restock_reminder' %}selected{% endif %}>Pengingat Restock</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select" onchange="this.form.submit()">
                        <option value="">Semua Status</option>
                        <option value="unread" {% if selected_status == 'unread' %}selected{% endif %}>Belum Dibaca</option>
                        <option value="read" {% if selected_status == 'read' %}selected{% endif %}>Sudah Dibaca</option>
                        <option value="dismissed" {% if selected_status == 'dismissed' %}selected{% endif %}>Diabaikan</option>
                    </select>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Daftar Notifikasi -->
    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Tanggal</th>
                        <th>Produk</th>
                        <th>Tipe</th>
                        <th>Pesan</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    {% for notification in notifications %}
                    <tr>
                        <td>{{ notification.created_at|date:"d/m/Y H:i" }}</td>
                        <td>
                            <a href="{% url 'dashboard:product_detail' notification.product.id %}" class="text-primary">
                                {{ notification.product.name }}
                            </a>
                        </td>
                        <td>
                            {% if notification.type == 'low_stock' %}
                            <span class="badge badge-warning">
                                <i class="fas fa-exclamation-triangle"></i> Stok Rendah
                            </span>
                            {% elif notification.type == 'out_of_stock' %}
                            <span class="badge badge-danger">
                                <i class="fas fa-times-circle"></i> Stok Habis
                            </span>
                            {% elif notification.type == 'restock_reminder' %}
                            <span class="badge badge-info">
                                <i class="fas fa-sync-alt"></i> Pengingat Restock
                            </span>
                            {% endif %}
                        </td>
                        <td>{{ notification.message }}</td>
                        <td>
                            {% if notification.status == 'unread' %}
                            <span class="badge badge-warning">Belum Dibaca</span>
                            {% elif notification.status == 'read' %}
                            <span class="badge badge-success">Sudah Dibaca</span>
                            {% else %}
                            <span class="badge badge-secondary">Diabaikan</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'dashboard:notification_mark_read' notification.id %}" class="btn-icon view tooltip">
                                    <i class="fas fa-check"></i>
                                    <span class="tooltip-text">Tandai Dibaca</span>
                                </a>
                                <a href="{% url 'dashboard:notification_dismiss' notification.id %}" class="btn-icon delete tooltip">
                                    <i class="fas fa-times"></i>
                                    <span class="tooltip-text">Abaikan</span>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6">
                            <div class="empty-state">
                                <i class="fas fa-bell-slash"></i>
                                <p>Tidak ada notifikasi.</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    {% if notifications.has_other_pages %}
    <nav aria-label="Page navigation">
        <ul class="pagination">
            {% if notifications.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ notifications.previous_page_number }}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}
            
            {% for i in notifications.paginator.page_range %}
                {% if notifications.number == i %}
                <li class="page-item active">
                    <span class="page-link">{{ i }}</span>
                </li>
                {% elif i > notifications.number|add:'-3' and i < notifications.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}">{{ i }}</a>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if notifications.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ notifications.next_page_number }}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ notifications.paginator.num_pages }}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}








