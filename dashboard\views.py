import json
from django.db.models import Sum, Count, F, Q, Value, DecimalField, Case, When, BooleanField, DurationField, ExpressionWrapper
from django.db.models.functions import <PERSON>runcDay, TruncWeek, TruncMonth, Coalesce
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.contrib.auth.models import User
from django.db import transaction
from django import forms
from .models import Product, StockMovement, Category, UnitOfMeasure, Notification
from .forms import ProductForm, StockMovementForm, UserForm, ProductSearchForm, StockMovementSearchForm
from django.core.paginator import Paginator
from .utils import require_admin_or_owner, require_owner, is_admin_or_owner, is_owner
from django.conf import settings as django_settings

# Authentication views
def user_login(request):
    if request.user.is_authenticated:
        return redirect('dashboard:index')
        
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        
        if user is not None:
            login(request, user)
            return redirect('dashboard:index')
        else:
            messages.error(request, 'Username atau password salah.')
    
    return render(request, 'dashboard/auth/login.html')

def user_logout(request):
    logout(request)
    return redirect('dashboard:login')

# Check if user is admin
def is_admin(user):
    return user.is_staff

# User management views
@login_required
@require_owner
def user_list(request):
    users = User.objects.all().order_by('username')
    return render(request, 'dashboard/auth/user_list.html', {'users': users})

@login_required
@require_owner
def user_create(request):
    if request.method == 'POST':
        form = UserForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            password = form.cleaned_data.get('password')
            role = form.cleaned_data.get('role')
            
            # Set password
            if password:
                user.set_password(password)
            
            # Set is_staff berdasarkan role
            if role in ['admin', 'owner']:
                user.is_staff = True
            else:
                user.is_staff = False
                
            # Set is_superuser untuk owner
            if role == 'owner':
                user.is_superuser = True
            else:
                user.is_superuser = False
            
            user.save()
            messages.success(request, f'Pengguna {user.username} dengan role {role} berhasil dibuat.')
            return redirect('dashboard:user_list')
    else:
        form = UserForm()
    
    return render(request, 'dashboard/auth/user_form.html', {
        'form': form,
        'title': 'Tambah Pengguna Baru'
    })

@login_required
@require_owner
def user_edit(request, pk):
    user = get_object_or_404(User, pk=pk)
    
    if request.method == 'POST':
        form = UserForm(request.POST, instance=user)
        if form.is_valid():
            user = form.save(commit=False)
            password = form.cleaned_data.get('password')
            role = form.cleaned_data.get('role')
            
            # Set password jika diisi
            if password:
                user.set_password(password)
            
            # Set is_staff berdasarkan role
            if role in ['admin', 'owner']:
                user.is_staff = True
            else:
                user.is_staff = False
                
            # Set is_superuser untuk owner
            if role == 'owner':
                user.is_superuser = True
            else:
                user.is_superuser = False
            
            user.save()
            messages.success(request, f'Pengguna {user.username} berhasil diperbarui.')
            return redirect('dashboard:user_list')
    else:
        form = UserForm(instance=user)
        form.fields['password'].required = False
        
        # Set initial value untuk role berdasarkan user permissions
        if user.is_superuser:
            form.fields['role'].initial = 'owner'
        elif user.is_staff:
            form.fields['role'].initial = 'admin'
        else:
            form.fields['role'].initial = 'user'
    
    return render(request, 'dashboard/auth/user_form.html', {
        'form': form,
        'user_obj': user,
        'title': 'Edit Pengguna'
    })

@login_required
@require_owner
def user_delete(request, pk):
    user = get_object_or_404(User, pk=pk)
    
    if request.user == user:
        messages.error(request, 'Anda tidak dapat menghapus akun Anda sendiri.')
        return redirect('dashboard:user_list')
    
    if request.method == 'POST':
        username = user.username
        user.delete()
        messages.success(request, f'Pengguna {username} berhasil dihapus.')
        return redirect('dashboard:user_list')
    
    return render(request, 'dashboard/auth/user_confirm_delete.html', {
        'user_obj': user
    })

# Tambahkan login_required ke semua view yang memerlukan autentikasi
@login_required
def dashboard(request):
    # Ringkasan stok
    total_products = Product.objects.count()
    total_stock = Product.objects.aggregate(total=Sum('quantity'))['total'] or 0
    low_stock_products = Product.objects.filter(quantity__lte=F('min_stock_level')).count()
    
    # Hitung jumlah produk yang akan kedaluwarsa dalam 30 hari
    today = timezone.now().date()
    expiry_threshold = today + timedelta(days=30)
    expiring_products_count = Product.objects.filter(
        expiry_date__isnull=False,
        expiry_date__gt=today,
        expiry_date__lte=expiry_threshold,
        quantity__gt=0  # Hanya produk dengan stok > 0
    ).count()
    
    # Hitung jumlah pengingat restock
    restock_reminder_count = Notification.objects.filter(
        type='restock_reminder',
        status__in=['unread', 'read']
    ).count()
    
    # Data untuk grafik (stok masuk & keluar per hari)
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=7)
    
    # Persiapkan data untuk grafik
    dates = []
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date)
        current_date += timedelta(days=1)
    
    # Format label untuk grafik (tanggal dalam format dd/mm)
    labels = [date.strftime('%d/%m') for date in dates]
    
    # Ambil data stok masuk per hari
    stock_in = StockMovement.objects.filter(
        movement_type='IN',
        timestamp__date__gte=start_date,
        timestamp__date__lte=end_date
    ).annotate(
        day=TruncDay('timestamp')
    ).values('day').annotate(
        total=Sum('quantity')
    ).order_by('day')
    
    # Ambil data stok keluar per hari
    stock_out = StockMovement.objects.filter(
        movement_type='OUT',
        timestamp__date__gte=start_date,
        timestamp__date__lte=end_date
    ).annotate(
        day=TruncDay('timestamp')
    ).values('day').annotate(
        total=Sum('quantity')
    ).order_by('day')
    
    # Konversi queryset ke dictionary untuk mempermudah akses
    stock_in_dict = {item['day'].date(): item['total'] for item in stock_in}
    stock_out_dict = {item['day'].date(): item['total'] for item in stock_out}
    
    # Buat array data untuk grafik
    stock_in_data = []
    stock_out_data = []
    
    for date in dates:
        stock_in_data.append(stock_in_dict.get(date, 0))
        stock_out_data.append(stock_out_dict.get(date, 0))
    
    # Produk dengan stok rendah untuk ditampilkan di dashboard
    low_stock_items = Product.objects.filter(
        quantity__lte=F('min_stock_level')
    ).annotate(
        is_empty=Case(
            When(quantity=0, then=Value(True)),
            default=Value(False),
            output_field=BooleanField()
        )
    ).order_by('is_empty', 'quantity').select_related('unit')[:10]
    
    # Produk yang akan kedaluwarsa dalam 30 hari
    today = timezone.now().date()
    expiry_threshold = today + timedelta(days=30)
    
    expiring_products = Product.objects.filter(
        expiry_date__isnull=False,
        expiry_date__gt=today,
        expiry_date__lte=expiry_threshold,
        quantity__gt=0  # Hanya produk dengan stok > 0
    ).annotate(
        days_until_expiry=ExpressionWrapper(
            F('expiry_date') - Value(today),
            output_field=DurationField()
        )
    ).order_by('expiry_date').select_related('unit')[:10]
    
    # Konversi days_until_expiry dari timedelta ke integer hari
    for product in expiring_products:
        product.days_until_expiry = product.days_until_expiry.days
    
    # Produk terbaru untuk ditampilkan di dashboard
    recent_products = Product.objects.all().order_by('-created_at')[:5]
    
    context = {
        'total_products': total_products,
        'total_stock': total_stock,
        'low_stock_products': low_stock_products,
        'expiring_products_count': expiring_products_count,  # Gunakan nama variabel yang berbeda
        'restock_reminder_count': restock_reminder_count,
        'low_stock_items': low_stock_items,
        'expiring_products': expiring_products,
        'recent_products': recent_products,  # Tambahkan produk terbaru ke context
        'labels': json.dumps(labels),
        'stock_in_data': json.dumps(stock_in_data),
        'stock_out_data': json.dumps(stock_out_data),
    }
    
    return render(request, 'dashboard/index.html', context)

@login_required
@require_admin_or_owner
def product_list(request):
    products = Product.objects.all()
    form = ProductSearchForm(request.GET)
    
    # Hitung statistik produk
    total_products = products.count()
    available_count = products.filter(quantity__gt=0).count()
    low_stock_count = products.filter(quantity__lte=F('min_stock_level'), quantity__gt=0).count()
    out_of_stock_count = products.filter(quantity=0).count()
    
    if form.is_valid():
        # Filter berdasarkan pencarian
        search_query = form.cleaned_data.get('search')
        if search_query:
            products = products.filter(
                Q(name__icontains=search_query) | 
                Q(code__icontains=search_query) |
                Q(description__icontains=search_query)
            )
        
        # Filter berdasarkan kategori
        category = form.cleaned_data.get('category')
        if category:
            products = products.filter(category=category)
        
        # Filter berdasarkan status stok
        stock_status = form.cleaned_data.get('stock_status')
        if stock_status == 'low':
            products = products.filter(quantity__lte=F('min_stock_level'), quantity__gt=0)
        elif stock_status == 'out':
            products = products.filter(quantity=0)
        elif stock_status == 'normal':
            products = products.filter(quantity__gt=F('min_stock_level'))
        
        # Filter berdasarkan status kedaluwarsa
        today = timezone.now().date()
        expiry_status = form.cleaned_data.get('expiry_status')
        if expiry_status == 'expiring':
            expiry_threshold = today + timedelta(days=30)
            products = products.filter(
                expiry_date__isnull=False,
                expiry_date__gt=today,
                expiry_date__lte=expiry_threshold
            )
        elif expiry_status == 'expired':
            products = products.filter(
                expiry_date__isnull=False,
                expiry_date__lte=today
            )
        elif expiry_status == 'not_expired':
            products = products.filter(
                Q(expiry_date__isnull=True) | 
                Q(expiry_date__gt=today)
            )
    
    # Urutkan produk berdasarkan nama
    products = products.order_by('name')
    
    context = {
        'products': products,
        'form': form,
        'total_products': total_products,
        'available_count': available_count,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count
    }
    
    return render(request, 'dashboard/product_list.html', context)

@login_required
@require_admin_or_owner
def product_create(request):
    if request.method == 'POST':
        form = ProductForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Produk berhasil ditambahkan.')
            return redirect('dashboard:product_list')
    else:
        form = ProductForm()
    
    return render(request, 'dashboard/product_form.html', {
        'form': form,
        'title': 'Tambah Produk Baru'
    })

@login_required
@require_admin_or_owner
def product_edit(request, pk):
    product = get_object_or_404(Product, pk=pk)
    
    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, 'Produk berhasil diperbarui.')
            return redirect('dashboard:product_list')
    else:
        form = ProductForm(instance=product)
    
    return render(request, 'dashboard/product_form.html', {
        'form': form,
        'product': product,
        'title': 'Edit Produk'
    })

# Tambahkan alias untuk product_edit
product_update = product_edit

@login_required
@require_admin_or_owner
def product_delete(request, pk):
    product = get_object_or_404(Product, pk=pk)
    
    if request.method == 'POST':
        product.delete()
        messages.success(request, 'Produk berhasil dihapus.')
        return redirect('dashboard:product_list')
    
    return render(request, 'dashboard/product_confirm_delete.html', {
        'product': product
    })

@login_required
@require_admin_or_owner
def product_detail(request, pk):
    product = get_object_or_404(Product, pk=pk)
    
    # Ambil riwayat pergerakan stok
    stock_movements = StockMovement.objects.filter(product=product).order_by('-timestamp')[:10]
    
    # Ambil notifikasi terkait produk
    product_notifications = Notification.objects.filter(product=product).order_by('-created_at')[:5]
    
    context = {
        'product': product,
        'stock_movements': stock_movements,
        'product_notifications': product_notifications,
    }
    
    return render(request, 'dashboard/product_detail.html', context)

@login_required
@require_admin_or_owner
def stock_movement_list(request):
    # Inisialisasi form pencarian dengan data dari request
    form = StockMovementSearchForm(request.GET)
    
    # Mulai dengan semua pergerakan stok
    movements = StockMovement.objects.all()
    
    # Terapkan filter jika form valid
    if form.is_valid():
        # Filter berdasarkan pencarian teks (nama produk)
        search_query = form.cleaned_data.get('search')
        if search_query:
            movements = movements.filter(product__name__icontains=search_query)
        
        # Filter berdasarkan tipe pergerakan
        movement_type = form.cleaned_data.get('movement_type')
        if movement_type:
            movements = movements.filter(movement_type=movement_type)
        
        # Filter berdasarkan tanggal
        date_from = form.cleaned_data.get('date_from')
        if date_from:
            movements = movements.filter(timestamp__date__gte=date_from)
        
        date_to = form.cleaned_data.get('date_to')
        if date_to:
            movements = movements.filter(timestamp__date__lte=date_to)
    
    # Urutkan pergerakan stok berdasarkan timestamp terbaru
    movements = movements.order_by('-timestamp')
    
    context = {
        'movements': movements,
        'form': form
    }
    
    return render(request, 'dashboard/stock_movement_list.html', context)

@login_required
@require_admin_or_owner
@transaction.atomic
def stock_in(request):
    initial_data = {'movement_type': 'IN'}
    
    # Cek apakah ada parameter produk di URL
    product_id = request.GET.get('product')
    if product_id:
        try:
            product = Product.objects.get(pk=product_id)
            initial_data['product'] = product
        except Product.DoesNotExist:
            pass
    
    if request.method == 'POST':
        form = StockMovementForm(request.POST)
        if form.is_valid():
            # Simpan pergerakan stok
            stock_movement = form.save(commit=False)
            stock_movement.movement_type = 'IN'
            stock_movement.save()
            
            # Update stok produk
            product = stock_movement.product
            old_quantity = product.quantity
            product.quantity += stock_movement.quantity
            product.save()
            
            messages.success(request, f'Stok masuk untuk {product.name} berhasil dicatat.')
            return redirect('dashboard:stock_movement_list')
    else:
        form = StockMovementForm(initial=initial_data)
        # Sembunyikan field movement_type karena sudah ditentukan
        form.fields['movement_type'].widget = forms.HiddenInput()
    
    return render(request, 'dashboard/stock_movement_form.html', {
        'form': form,
        'title': 'Tambah Stok Masuk'
    })

@login_required
@require_admin_or_owner
@transaction.atomic
def stock_out(request):
    initial_data = {'movement_type': 'OUT'}
    
    # Cek apakah ada parameter produk di URL
    product_id = request.GET.get('product')
    if product_id:
        try:
            product = Product.objects.get(pk=product_id)
            initial_data['product'] = product
        except Product.DoesNotExist:
            pass
    
    if request.method == 'POST':
        form = StockMovementForm(request.POST)
        if form.is_valid():
            # Validasi stok cukup
            product = form.cleaned_data['product']
            quantity = form.cleaned_data['quantity']
            
            if product.quantity < quantity:
                form.add_error('quantity', f'Stok tidak cukup. Stok saat ini: {product.quantity}')
            else:
                # Simpan pergerakan stok
                stock_movement = form.save(commit=False)
                stock_movement.movement_type = 'OUT'
                stock_movement.save()
                
                # Update stok produk
                old_quantity = product.quantity
                product.quantity -= stock_movement.quantity
                product.save()
                
                messages.success(request, f'Stok keluar untuk {product.name} berhasil dicatat.')
                return redirect('dashboard:stock_movement_list')
    else:
        form = StockMovementForm(initial=initial_data)
        # Sembunyikan field movement_type karena sudah ditentukan
        form.fields['movement_type'].widget = forms.HiddenInput()
    
    return render(request, 'dashboard/stock_movement_form.html', {
        'form': form,
        'title': 'Tambah Stok Keluar'
    })

# Laporan Stok
@login_required
@require_admin_or_owner
def stock_report(request):
    # Filter berdasarkan periode
    period = request.GET.get('period', 'daily')
    
    # Tentukan tanggal awal berdasarkan periode
    today = timezone.now().date()
    if period == 'weekly':
        start_date = today - timedelta(days=7)
        trunc_func = TruncWeek
        date_format = '%d %b %Y'
    elif period == 'monthly':
        start_date = today - timedelta(days=30)
        trunc_func = TruncMonth
        date_format = '%b %Y'
    else:  # daily
        start_date = today - timedelta(days=7)  # Default 7 hari terakhir
        trunc_func = TruncDay
        date_format = '%d %b %Y'
    
    # Ambil data stok per periode
    stock_data = StockMovement.objects.filter(
        timestamp__date__gte=start_date
    ).annotate(
        period=trunc_func('timestamp')
    ).values('period').annotate(
        stock_in=Sum('quantity', filter=Q(movement_type='IN')),
        stock_out=Sum('quantity', filter=Q(movement_type='OUT'))
    ).order_by('period')
    
    # Format data untuk chart
    periods = []
    stock_in_data = []
    stock_out_data = []
    
    for item in stock_data:
        period_str = item['period'].strftime(date_format)
        periods.append(period_str)
        stock_in_data.append(item['stock_in'] or 0)
        stock_out_data.append(item['stock_out'] or 0)
    
    # Ambil produk dengan stok terendah
    low_stock_products = Product.objects.filter(
        quantity__lte=F('min_stock_level')
    ).order_by('quantity')[:10]
    
    # Ambil produk dengan stok tertinggi
    high_stock_products = Product.objects.order_by('-quantity')[:10]
    
    context = {
        'period': period,
        'periods_json': json.dumps(periods),
        'stock_in_json': json.dumps(stock_in_data),
        'stock_out_json': json.dumps(stock_out_data),
        'low_stock_products': low_stock_products,
        'high_stock_products': high_stock_products,
    }
    
    return render(request, 'dashboard/reports/stock_report.html', context)

# Laporan Transaksi
@login_required
@require_owner
def transaction_report(request):
    # Filter berdasarkan periode
    period = request.GET.get('period', 'daily')
    movement_type = request.GET.get('movement_type', '')
    product_id = request.GET.get('product', '')
    
    # Tentukan tanggal awal berdasarkan periode
    today = timezone.now().date()
    if period == 'weekly':
        start_date = today - timedelta(days=7)
        trunc_func = TruncWeek
        date_format = '%d %b %Y'
    elif period == 'monthly':
        start_date = today - timedelta(days=30)
        trunc_func = TruncMonth
        date_format = '%b %Y'
    else:  # daily
        start_date = today - timedelta(days=7)  # Default 7 hari terakhir
        trunc_func = TruncDay
        date_format = '%d %b %Y'
    
    # Filter berdasarkan tipe pergerakan dan produk
    movements_query = StockMovement.objects.filter(timestamp__date__gte=start_date)
    
    if movement_type:
        movements_query = movements_query.filter(movement_type=movement_type)
    
    if product_id:
        movements_query = movements_query.filter(product_id=product_id)
    
    # Ambil data untuk grafik
    movement_data = movements_query.annotate(
        period=trunc_func('timestamp')
    ).values('period', 'movement_type').annotate(
        total=Sum('quantity')
    ).order_by('period', 'movement_type')
    
    # Format data untuk chart
    periods_set = set()
    in_data = {}
    out_data = {}
    
    for item in movement_data:
        period_str = item['period'].strftime(date_format)
        periods_set.add(period_str)
        
        if item['movement_type'] == 'IN':
            in_data[period_str] = item['total']
        else:
            out_data[period_str] = item['total']
    
    # Urutkan periode
    periods = sorted(list(periods_set))
    
    # Buat array data lengkap dengan nilai 0 untuk periode yang tidak ada data
    in_data_array = [in_data.get(period, 0) for period in periods]
    out_data_array = [out_data.get(period, 0) for period in periods]
    
    # Hitung total untuk ringkasan
    total_in = movements_query.filter(movement_type='IN').aggregate(total=Sum('quantity'))['total'] or 0
    total_out = movements_query.filter(movement_type='OUT').aggregate(total=Sum('quantity'))['total'] or 0
    total_transactions = movements_query.count()
    
    # Ambil semua produk untuk filter
    all_products = Product.objects.all().order_by('name')
    
    # Ambil data transaksi untuk tabel
    movements = movements_query.select_related('product').order_by('-timestamp')[:100]  # Batasi 100 transaksi terbaru
    
    context = {
        'period': period,
        'movement_type': movement_type,
        'product_id': product_id,
        'all_products': all_products,
        'movements': movements,
        'total_in': total_in,
        'total_out': total_out,
        'total_transactions': total_transactions,
        'periods_json': json.dumps(periods),
        'in_data_json': json.dumps(in_data_array),
        'out_data_json': json.dumps(out_data_array),
    }
    
    return render(request, 'dashboard/reports/transaction_report.html', context)

# Laporan Keuangan
@login_required
@require_owner
def financial_report(request):
    # Filter berdasarkan periode
    period = request.GET.get('period', 'daily')
    
    # Tentukan tanggal awal berdasarkan periode
    today = timezone.now().date()
    if period == 'weekly':
        start_date = today - timedelta(days=30)  # Perluas rentang untuk data mingguan
        trunc_func = TruncWeek
        date_format = '%d %b %Y'
    elif period == 'monthly':
        start_date = today - timedelta(days=90)  # Perluas rentang untuk data bulanan
        trunc_func = TruncMonth
        date_format = '%b %Y'
    else:  # daily
        start_date = today - timedelta(days=14)  # Perluas rentang untuk data harian
        trunc_func = TruncDay
        date_format = '%d %b %Y'
    
    # Jika tidak ada data transaksi, buat data dummy untuk contoh
    if StockMovement.objects.count() == 0:
        # Buat data dummy jika tidak ada data
        dummy_periods = ["1 Mei 2025", "2 Mei 2025", "3 Mei 2025", "4 Mei 2025", "5 Mei 2025"]
        dummy_purchase = [500000, 450000, 600000, 550000, 700000]
        dummy_sales = [750000, 800000, 850000, 900000, 950000]
        dummy_profit = [250000, 350000, 250000, 350000, 250000]
        
        context = {
            'period': period,
            'periods_json': json.dumps(dummy_periods),
            'purchase_json': json.dumps(dummy_purchase),
            'sales_json': json.dumps(dummy_sales),
            'profit_json': json.dumps(dummy_profit),
            'summary': {
                'total_purchase': sum(dummy_purchase),
                'total_sales': sum(dummy_sales),
                'total_profit': sum(dummy_profit),
            },
        }
        
        return render(request, 'dashboard/reports/financial_report.html', context)
    
    # Ambil data keuangan
    # Barang masuk (modal)
    purchase_data_query = StockMovement.objects.filter(
        timestamp__date__gte=start_date,
        movement_type='IN'
    ).annotate(
        period=trunc_func('timestamp')
    ).values('period').annotate(
        total=Coalesce(Sum(F('quantity') * F('product__purchase_price')), Value(0, output_field=DecimalField()))
    ).order_by('period')
    
    # Barang keluar (penjualan)
    sales_data_query = StockMovement.objects.filter(
        timestamp__date__gte=start_date,
        movement_type='OUT'
    ).annotate(
        period=trunc_func('timestamp')
    ).values('period').annotate(
        total=Coalesce(Sum(F('quantity') * F('product__selling_price')), Value(0, output_field=DecimalField()))
    ).order_by('period')
    
    # Format data untuk chart
    periods_set = set()
    purchase_data_dict = {}
    sales_data_dict = {}
    
    for item in purchase_data_query:
        period_str = item['period'].strftime(date_format)
        periods_set.add(period_str)
        purchase_data_dict[period_str] = float(item['total'])
    
    for item in sales_data_query:
        period_str = item['period'].strftime(date_format)
        periods_set.add(period_str)
        sales_data_dict[period_str] = float(item['total'])
    
    # Urutkan periode
    periods = sorted(list(periods_set))
    
    # Buat array data lengkap dengan nilai 0 untuk periode yang tidak ada data
    purchase_data = [purchase_data_dict.get(period, 0) for period in periods]
    sales_data = [sales_data_dict.get(period, 0) for period in periods]
    
    # Hitung keuntungan
    profit_data = [sales - purchase for sales, purchase in zip(sales_data, purchase_data)]
    
    # Ringkasan keuangan
    summary = {
        'total_purchase': sum(purchase_data),
        'total_sales': sum(sales_data),
        'total_profit': sum(profit_data),
    }
    
    context = {
        'period': period,
        'periods_json': json.dumps(periods),
        'purchase_json': json.dumps(purchase_data),
        'sales_json': json.dumps(sales_data),
        'profit_json': json.dumps(profit_data),
        'summary': summary,
    }
    
    return render(request, 'dashboard/reports/financial_report.html', context)

@login_required
def notification_list(request):
    """Tampilkan daftar notifikasi"""
    # Filter berdasarkan tipe dan status jika ada di query params
    selected_type = request.GET.get('type', '')
    selected_status = request.GET.get('status', '')
    
    # Query dasar
    notifications = Notification.objects.all()
    
    # Terapkan filter jika ada
    if selected_type:
        notifications = notifications.filter(type=selected_type)
    
    if selected_status:
        notifications = notifications.filter(status=selected_status)
    
    # Hitung total untuk statistik
    total_notifications = Notification.objects.count()
    unread_count = Notification.objects.filter(status='unread').count()
    read_count = Notification.objects.filter(status='read').count()
    dismissed_count = Notification.objects.filter(status='dismissed').count()
    
    # Hitung jumlah notifikasi berdasarkan tipe
    low_stock_count = Notification.objects.filter(type='low_stock', status__in=['unread', 'read']).count()
    out_of_stock_count = Notification.objects.filter(type='out_of_stock', status__in=['unread', 'read']).count()
    restock_reminder_count = Notification.objects.filter(type='restock_reminder', status__in=['unread', 'read']).count()
    
    # Urutkan berdasarkan tanggal terbaru
    notifications = notifications.order_by('-created_at')
    
    # Tambahkan pagination
    paginator = Paginator(notifications, 10)  # 10 notifikasi per halaman
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'notifications': page_obj,
        'total_notifications': total_notifications,
        'unread_count': unread_count,
        'read_count': read_count,
        'dismissed_count': dismissed_count,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'restock_reminder_count': restock_reminder_count,
        'selected_type': selected_type,
        'selected_status': selected_status,
        'debug': django_settings.DEBUG,  # Gunakan django_settings sebagai gantinya
    }
    
    return render(request, 'dashboard/notification_list.html', context)

@login_required
def mark_notification_as_read(request, pk):
    notification = get_object_or_404(Notification, pk=pk)
    notification.mark_as_read()
    
    # Redirect ke halaman produk jika ada
    if notification.product:
        return redirect('dashboard:product_detail', pk=notification.product.id)
    
    return redirect('dashboard:notification_list')

@login_required
def dismiss_notification(request, pk):
    notification = get_object_or_404(Notification, pk=pk)
    notification.dismiss()
    
    # Redirect kembali ke halaman notifikasi
    return redirect('dashboard:notification_list')

@login_required
def mark_all_as_read(request):
    """Tandai semua notifikasi sebagai sudah dibaca"""
    # Filter berdasarkan tipe dan status jika ada di query params
    selected_type = request.GET.get('type', '')
    selected_status = request.GET.get('status', '')
    
    # Query dasar
    notifications = Notification.objects.filter(status='unread')
    
    # Terapkan filter jika ada
    if selected_type:
        notifications = notifications.filter(type=selected_type)
    
    # Update semua notifikasi yang belum dibaca menjadi sudah dibaca
    count = notifications.count()
    notifications.update(status='read')
    
    messages.success(request, f"{count} notifikasi telah ditandai sebagai dibaca.")
    
    # Redirect kembali ke halaman notifikasi dengan filter yang sama
    redirect_url = 'dashboard:notification_list'
    if selected_type or selected_status:
        return redirect(f"{redirect_url}?type={selected_type}&status={selected_status}")
    return redirect(redirect_url)

@login_required
def dismiss_all_notifications(request):
    """Abaikan semua notifikasi"""
    # Filter berdasarkan tipe dan status jika ada di query params
    selected_type = request.GET.get('type', '')
    selected_status = request.GET.get('status', '')
    
    # Query dasar
    notifications = Notification.objects.filter(status__in=['unread', 'read'])
    
    # Terapkan filter jika ada
    if selected_type:
        notifications = notifications.filter(type=selected_type)
    
    if selected_status:
        notifications = notifications.filter(status=selected_status)
    
    # Update semua notifikasi menjadi diabaikan
    count = notifications.count()
    notifications.update(status='dismissed')
    
    messages.success(request, f"{count} notifikasi telah diabaikan.")
    
    # Redirect kembali ke halaman notifikasi dengan filter yang sama
    redirect_url = 'dashboard:notification_list'
    if selected_type or selected_status:
        return redirect(f"{redirect_url}?type={selected_type}&status={selected_status}")
    return redirect(redirect_url)

# Tambahkan view untuk kategori jika belum ada
@login_required
def category_list(request):
    categories = Category.objects.all()
    return render(request, 'dashboard/category_list.html', {'categories': categories})

@login_required
def category_create(request):
    if request.method == 'POST':
        # Implementasi pembuatan kategori
        pass
    return render(request, 'dashboard/category_form.html')

@login_required
def category_update(request, pk):
    category = get_object_or_404(Category, pk=pk)
    if request.method == 'POST':
        # Implementasi update kategori
        pass
    return render(request, 'dashboard/category_form.html', {'category': category})

@login_required
def category_delete(request, pk):
    category = get_object_or_404(Category, pk=pk)
    if request.method == 'POST':
        # Implementasi penghapusan kategori
        pass
    return render(request, 'dashboard/category_confirm_delete.html', {'category': category})

# Tambahkan view untuk UOM jika belum ada
@login_required
def uom_list(request):
    uoms = UnitOfMeasure.objects.all()
    return render(request, 'dashboard/uom_list.html', {'uoms': uoms})

@login_required
def uom_create(request):
    if request.method == 'POST':
        # Implementasi pembuatan UOM
        pass
    return render(request, 'dashboard/uom_form.html')

@login_required
def uom_update(request, pk):
    uom = get_object_or_404(UnitOfMeasure, pk=pk)
    if request.method == 'POST':
        # Implementasi update UOM
        pass
    return render(request, 'dashboard/uom_form.html', {'uom': uom})

@login_required
def uom_delete(request, pk):
    uom = get_object_or_404(UnitOfMeasure, pk=pk)
    if request.method == 'POST':
        # Implementasi penghapusan UOM
        pass
    return render(request, 'dashboard/uom_confirm_delete.html', {'uom': uom})

# Tambahkan view untuk settings jika belum ada
@login_required
def settings(request):
    return render(request, 'dashboard/settings.html')

# Tambahkan view untuk laporan jika belum ada
@login_required
def report_stock(request):
    return render(request, 'dashboard/report_stock.html')

@login_required
def report_movement(request):
    return render(request, 'dashboard/report_movement.html')

@login_required
def create_test_notifications(request):
    """
    Fungsi untuk membuat notifikasi dummy untuk testing
    Hanya untuk development
    """
    if not django_settings.DEBUG:
        messages.error(request, "Fungsi ini hanya tersedia dalam mode development.")
        return redirect('dashboard:notification_list')
    
    # Ambil semua produk
    products = Product.objects.all()[:5]  # Ambil 5 produk pertama
    
    if not products:
        messages.error(request, "Tidak ada produk untuk membuat notifikasi test.")
        return redirect('dashboard:notification_list')
    
    # Buat notifikasi dummy untuk setiap produk
    for product in products:
        # Stok rendah
        Notification.objects.create(
            product=product,
            type='low_stock',
            message=f"Stok produk {product.name} rendah. Sisa stok: {product.quantity}, minimum: {product.min_stock_level}."
        )
        
        # Stok habis
        Notification.objects.create(
            product=product,
            type='out_of_stock',
            message=f"Stok produk {product.name} habis. Segera lakukan restock."
        )
        
        # Pengingat restock
        Notification.objects.create(
            product=product,
            type='restock_reminder',
            message=f"Produk {product.name} telah di-restock. Stok saat ini: {product.quantity}."
        )
    
    messages.success(request, f"Berhasil membuat {len(products) * 3} notifikasi test.")
    return redirect('dashboard:notification_list')
