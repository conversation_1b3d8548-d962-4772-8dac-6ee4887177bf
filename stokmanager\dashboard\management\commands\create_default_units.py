from django.core.management.base import BaseCommand
from dashboard.models import UnitOfMeasure

class Command(BaseCommand):
    help = 'Membuat satuan default (pcs, lusin, kg, liter, dll)'

    def handle(self, *args, **options):
        units = [
            {'name': 'Pieces', 'abbreviation': 'pcs'},
            {'name': 'Lusin', 'abbreviation': 'lsn'},
            {'name': 'Kilogram', 'abbreviation': 'kg'},
            {'name': 'Gram', 'abbreviation': 'g'},
            {'name': 'Liter', 'abbreviation': 'L'},
            {'name': 'Mililiter', 'abbreviation': 'mL'},
            {'name': 'Box', 'abbreviation': 'box'},
            {'name': 'Pack', 'abbreviation': 'pack'},
        ]
        
        created_count = 0
        for unit_data in units:
            unit, created = UnitOfMeasure.objects.get_or_create(
                name=unit_data['name'],
                defaults={'abbreviation': unit_data['abbreviation']}
            )
            if created:
                created_count += 1
                self.stdout.write(f'Satuan "{unit.name}" berhasil dibuat.')
            else:
                self.stdout.write(f'Satuan "{unit.name}" sudah ada.')
        
        self.stdout.write(self.style.SUCCESS(f'{created_count} satuan berhasil dibuat.'))