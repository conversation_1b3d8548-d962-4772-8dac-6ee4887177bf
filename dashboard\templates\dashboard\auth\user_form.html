{% extends "dashboard/base.html" %} {% block title %}{{ title }}{% endblock %}
{% block content %}
<div class="container mt-4">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h4>{{ title }}</h4>
        </div>
        <div class="card-body">
          <form method="post">
            {% csrf_token %}

            <div class="mb-3">
              <label for="{{ form.username.id_for_label }}" class="form-label"
                >Username</label
              >
              {{ form.username }} {% if form.username.errors %}
              <div class="text-danger">{{ form.username.errors }}</div>
              {% endif %}
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label
                    for="{{ form.first_name.id_for_label }}"
                    class="form-label"
                    >Nama Depan</label
                  >
                  {{ form.first_name }} {% if form.first_name.errors %}
                  <div class="text-danger">{{ form.first_name.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label
                    for="{{ form.last_name.id_for_label }}"
                    class="form-label"
                    >Nama Belakang</label
                  >
                  {{ form.last_name }} {% if form.last_name.errors %}
                  <div class="text-danger">{{ form.last_name.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="{{ form.email.id_for_label }}" class="form-label"
                >Email</label
              >
              {{ form.email }} {% if form.email.errors %}
              <div class="text-danger">{{ form.email.errors }}</div>
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="{{ form.role.id_for_label }}" class="form-label"
                >Role</label
              >
              {{ form.role }} {% if form.role.errors %}
              <div class="text-danger">{{ form.role.errors }}</div>
              {% endif %}
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label
                    for="{{ form.password.id_for_label }}"
                    class="form-label"
                    >Password</label
                  >
                  {{ form.password }} {% if form.password.errors %}
                  <div class="text-danger">{{ form.password.errors }}</div>
                  {% endif %} {% if user_obj %}
                  <small class="form-text text-muted"
                    >Biarkan kosong jika tidak ingin mengubah password</small
                  >
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label
                    for="{{ form.confirm_password.id_for_label }}"
                    class="form-label"
                    >Konfirmasi Password</label
                  >
                  {{ form.confirm_password }} {% if form.confirm_password.errors
                  %}
                  <div class="text-danger">
                    {{ form.confirm_password.errors }}
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>

            <div class="mb-3 form-check">
              {{ form.is_staff }}
              <label
                class="form-check-label"
                for="{{ form.is_staff.id_for_label }}"
              >
                Admin
              </label>
              <small class="form-text text-muted d-block">
                Admin dapat mengelola pengguna dan memiliki akses ke semua fitur
              </small>
            </div>

            <div class="d-flex justify-content-between">
              <a
                href="{% url 'dashboard:user_list' %}"
                class="btn btn-secondary"
                >Batal</a
              >
              <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script>
  // Tambahkan class form-control ke semua input
  document.addEventListener("DOMContentLoaded", function () {
    const inputs = document.querySelectorAll(
      'input:not([type="checkbox"]), select'
    );
    inputs.forEach((input) => {
      input.classList.add("form-control");
    });

    // Tambahkan class form-check-input ke checkbox
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach((checkbox) => {
      checkbox.classList.add("form-check-input");
    });
  });
</script>
{% endblock %}
