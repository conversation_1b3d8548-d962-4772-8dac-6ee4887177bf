from django.core.management.base import BaseCommand
from django.conf import settings
from django.core.mail import send_mail
import os

class Command(BaseCommand):
    help = 'Setup email dengan berbagai provider alternatif'

    def add_arguments(self, parser):
        parser.add_argument(
            '--outlook',
            action='store_true',
            help='Setup untuk Outlook/Hotmail SMTP',
        )
        parser.add_argument(
            '--yahoo',
            action='store_true',
            help='Setup untuk Yahoo SMTP',
        )
        parser.add_argument(
            '--custom',
            action='store_true',
            help='Setup untuk SMTP custom',
        )
        parser.add_argument(
            '--back-to-console',
            action='store_true',
            help='Kembali ke console backend',
        )
        parser.add_argument(
            '--test-current',
            action='store_true',
            help='Test konfigurasi email saat ini',
        )

    def handle(self, *args, **options):
        if options['outlook']:
            self.setup_outlook()
        elif options['yahoo']:
            self.setup_yahoo()
        elif options['custom']:
            self.setup_custom()
        elif options['back_to_console']:
            self.back_to_console()
        elif options['test_current']:
            self.test_current()
        else:
            self.show_options()

    def show_options(self):
        self.stdout.write(self.style.SUCCESS('=== SETUP EMAIL ALTERNATIF ===\n'))
        
        current_backend = getattr(settings, 'EMAIL_BACKEND', 'Tidak diset')
        self.stdout.write(f'Backend saat ini: {current_backend}')
        
        self.stdout.write('\n📧 Pilihan setup email:')
        self.stdout.write('  --outlook         : Setup Outlook/Hotmail SMTP')
        self.stdout.write('  --yahoo           : Setup Yahoo SMTP')
        self.stdout.write('  --custom          : Setup SMTP custom')
        self.stdout.write('  --back-to-console : Kembali ke console backend')
        self.stdout.write('  --test-current    : Test konfigurasi saat ini')
        
        self.stdout.write('\n💡 Rekomendasi:')
        self.stdout.write('  1. Outlook/Hotmail lebih mudah setup daripada Gmail')
        self.stdout.write('  2. Yahoo juga support App Password')
        self.stdout.write('  3. Console backend untuk development/testing')

    def setup_outlook(self):
        self.stdout.write(self.style.SUCCESS('=== SETUP OUTLOOK/HOTMAIL SMTP ===\n'))
        
        self.stdout.write('📧 Outlook/Hotmail lebih mudah setup daripada Gmail')
        self.stdout.write('✅ Tidak perlu App Password untuk akun personal')
        self.stdout.write('⚠️  Untuk akun bisnis mungkin perlu App Password')
        
        email = input('\n📧 Masukkan email Outlook/Hotmail Anda: ')
        password = input('🔑 Masukkan password Outlook/Hotmail: ')
        
        if email and password:
            self.create_outlook_settings(email, password)
        else:
            self.stdout.write(self.style.ERROR('❌ Email dan password harus diisi'))

    def create_outlook_settings(self, email, password):
        settings_content = f'''
# Email settings untuk Outlook/Hotmail SMTP
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp-mail.outlook.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '{email}'
EMAIL_HOST_PASSWORD = '{password}'
DEFAULT_FROM_EMAIL = '{email}'
'''
        
        self.stdout.write('\n🔧 Konfigurasi Outlook SMTP:')
        self.stdout.write(f'   EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"')
        self.stdout.write(f'   EMAIL_HOST = "smtp-mail.outlook.com"')
        self.stdout.write(f'   EMAIL_PORT = 587')
        self.stdout.write(f'   EMAIL_USE_TLS = True')
        self.stdout.write(f'   EMAIL_HOST_USER = "{email}"')
        self.stdout.write(f'   EMAIL_HOST_PASSWORD = "{password}"')
        self.stdout.write(f'   DEFAULT_FROM_EMAIL = "{email}"')
        
        confirm = input('\n❓ Update settings.py dengan konfigurasi ini? (y/N): ')
        if confirm.lower() == 'y':
            self.update_settings_file('outlook', email, password)
        else:
            self.stdout.write('❌ Konfigurasi tidak diupdate')

    def setup_yahoo(self):
        self.stdout.write(self.style.SUCCESS('=== SETUP YAHOO SMTP ===\n'))
        
        self.stdout.write('📧 Yahoo memerlukan App Password')
        self.stdout.write('🔧 Langkah-langkah:')
        self.stdout.write('1. Buka Yahoo Account Security')
        self.stdout.write('2. Aktifkan 2-Step Verification')
        self.stdout.write('3. Generate App Password')
        self.stdout.write('4. Gunakan App Password, bukan password biasa')
        
        email = input('\n📧 Masukkan email Yahoo Anda: ')
        app_password = input('🔑 Masukkan App Password Yahoo: ')
        
        if email and app_password:
            self.create_yahoo_settings(email, app_password)
        else:
            self.stdout.write(self.style.ERROR('❌ Email dan App Password harus diisi'))

    def create_yahoo_settings(self, email, app_password):
        self.stdout.write('\n🔧 Konfigurasi Yahoo SMTP:')
        self.stdout.write(f'   EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"')
        self.stdout.write(f'   EMAIL_HOST = "smtp.mail.yahoo.com"')
        self.stdout.write(f'   EMAIL_PORT = 587')
        self.stdout.write(f'   EMAIL_USE_TLS = True')
        self.stdout.write(f'   EMAIL_HOST_USER = "{email}"')
        self.stdout.write(f'   EMAIL_HOST_PASSWORD = "{app_password}"')
        self.stdout.write(f'   DEFAULT_FROM_EMAIL = "{email}"')
        
        confirm = input('\n❓ Update settings.py dengan konfigurasi ini? (y/N): ')
        if confirm.lower() == 'y':
            self.update_settings_file('yahoo', email, app_password)
        else:
            self.stdout.write('❌ Konfigurasi tidak diupdate')

    def setup_custom(self):
        self.stdout.write(self.style.SUCCESS('=== SETUP SMTP CUSTOM ===\n'))
        
        host = input('📧 SMTP Host (contoh: smtp.example.com): ')
        port = input('🔌 SMTP Port (contoh: 587): ')
        use_tls = input('🔒 Gunakan TLS? (y/N): ').lower() == 'y'
        email = input('📧 Email: ')
        password = input('🔑 Password: ')
        
        if host and port and email and password:
            self.create_custom_settings(host, port, use_tls, email, password)
        else:
            self.stdout.write(self.style.ERROR('❌ Semua field harus diisi'))

    def create_custom_settings(self, host, port, use_tls, email, password):
        self.stdout.write('\n🔧 Konfigurasi SMTP Custom:')
        self.stdout.write(f'   EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"')
        self.stdout.write(f'   EMAIL_HOST = "{host}"')
        self.stdout.write(f'   EMAIL_PORT = {port}')
        self.stdout.write(f'   EMAIL_USE_TLS = {use_tls}')
        self.stdout.write(f'   EMAIL_HOST_USER = "{email}"')
        self.stdout.write(f'   EMAIL_HOST_PASSWORD = "{password}"')
        self.stdout.write(f'   DEFAULT_FROM_EMAIL = "{email}"')
        
        confirm = input('\n❓ Update settings.py dengan konfigurasi ini? (y/N): ')
        if confirm.lower() == 'y':
            self.update_settings_file('custom', email, password, host, port, use_tls)
        else:
            self.stdout.write('❌ Konfigurasi tidak diupdate')

    def back_to_console(self):
        self.stdout.write(self.style.SUCCESS('=== KEMBALI KE CONSOLE BACKEND ===\n'))
        
        confirm = input('❓ Kembali ke console backend? Email akan muncul di terminal (y/N): ')
        if confirm.lower() == 'y':
            self.update_settings_file('console')
            self.stdout.write(self.style.SUCCESS('✅ Berhasil kembali ke console backend'))
        else:
            self.stdout.write('❌ Tidak diubah')

    def update_settings_file(self, provider, email=None, password=None, host=None, port=None, use_tls=None):
        """Update settings.py dengan konfigurasi email baru"""
        
        # Baca file settings.py
        settings_path = 'stokmanager/settings.py'
        with open(settings_path, 'r') as f:
            content = f.read()
        
        # Cari dan replace bagian email settings
        if provider == 'console':
            new_email_config = '''# Email settings - Console Backend (Development)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
DEFAULT_FROM_EMAIL = '<EMAIL>'

# Untuk SMTP, uncomment dan sesuaikan:
# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_HOST = 'smtp.gmail.com'
# EMAIL_PORT = 587
# EMAIL_USE_TLS = True
# EMAIL_HOST_USER = '<EMAIL>'
# EMAIL_HOST_PASSWORD = 'your-password'
# DEFAULT_FROM_EMAIL = '<EMAIL>'

ADMIN_EMAIL = '<EMAIL>' '''
        
        elif provider == 'outlook':
            new_email_config = f'''# Email settings - Outlook SMTP
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp-mail.outlook.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '{email}'
EMAIL_HOST_PASSWORD = '{password}'
DEFAULT_FROM_EMAIL = '{email}'

# Untuk console backend, uncomment:
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

ADMIN_EMAIL = '{email}' '''
        
        elif provider == 'yahoo':
            new_email_config = f'''# Email settings - Yahoo SMTP
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.mail.yahoo.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '{email}'
EMAIL_HOST_PASSWORD = '{password}'
DEFAULT_FROM_EMAIL = '{email}'

# Untuk console backend, uncomment:
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

ADMIN_EMAIL = '{email}' '''
        
        elif provider == 'custom':
            new_email_config = f'''# Email settings - Custom SMTP
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = '{host}'
EMAIL_PORT = {port}
EMAIL_USE_TLS = {use_tls}
EMAIL_HOST_USER = '{email}'
EMAIL_HOST_PASSWORD = '{password}'
DEFAULT_FROM_EMAIL = '{email}'

# Untuk console backend, uncomment:
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

ADMIN_EMAIL = '{email}' '''
        
        # Replace bagian email settings
        import re
        pattern = r'# Email settings.*?ADMIN_EMAIL = .*?\n'
        new_content = re.sub(pattern, new_email_config + '\n', content, flags=re.DOTALL)
        
        # Tulis kembali ke file
        with open(settings_path, 'w') as f:
            f.write(new_content)
        
        self.stdout.write(self.style.SUCCESS('✅ settings.py berhasil diupdate'))

    def test_current(self):
        self.stdout.write(self.style.SUCCESS('=== TEST KONFIGURASI EMAIL SAAT INI ===\n'))
        
        backend = getattr(settings, 'EMAIL_BACKEND', 'Tidak diset')
        self.stdout.write(f'Backend: {backend}')
        
        if 'console' in backend.lower():
            self.stdout.write(self.style.WARNING('📺 Console Backend - Email akan muncul di terminal'))
        else:
            host = getattr(settings, 'EMAIL_HOST', 'Tidak diset')
            port = getattr(settings, 'EMAIL_PORT', 'Tidak diset')
            user = getattr(settings, 'EMAIL_HOST_USER', 'Tidak diset')
            self.stdout.write(f'Host: {host}:{port}')
            self.stdout.write(f'User: {user}')
            
            # Test koneksi
            try:
                from django.core.mail import get_connection
                self.stdout.write('\n🔌 Testing koneksi SMTP...')
                connection = get_connection()
                connection.open()
                self.stdout.write(self.style.SUCCESS('✅ Koneksi SMTP berhasil'))
                connection.close()
                
                # Test kirim email
                from dashboard.utils import get_admin_and_owner_emails
                recipient_emails = get_admin_and_owner_emails()
                if recipient_emails:
                    confirm = input(f'\n❓ Kirim email test ke {len(recipient_emails)} penerima? (y/N): ')
                    if confirm.lower() == 'y':
                        send_mail(
                            subject='[Stock Manager] Test Email SMTP',
                            message='Ini adalah email test untuk memastikan SMTP berfungsi dengan baik.',
                            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', user),
                            recipient_list=recipient_emails,
                            fail_silently=False,
                        )
                        self.stdout.write(self.style.SUCCESS('✅ Email test berhasil dikirim'))
                        self.stdout.write(f'📧 Cek inbox: {", ".join(recipient_emails)}')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'❌ Error: {e}'))
