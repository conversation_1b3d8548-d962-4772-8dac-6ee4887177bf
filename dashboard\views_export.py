import io
import json
from django.http import HttpResponse
from django.utils import timezone
from django.db.models import Sum, F, Q
from django.db.models.functions import TruncDay, TruncWeek, TruncMonth
from .models import StockMovement, Product
import openpyxl
from openpyxl.utils import get_column_letter
from xhtml2pdf import pisa
from django.template.loader import render_to_string

# Export Excel

def export_stock_report_excel(request):
    period = request.GET.get('period', 'daily')
    today = timezone.now().date()
    if period == 'weekly':
        start_date = today - timezone.timedelta(days=7)
        trunc_func = TruncWeek
        date_format = '%d %b %Y'
    elif period == 'monthly':
        start_date = today - timezone.timedelta(days=30)
        trunc_func = TruncMonth
        date_format = '%b %Y'
    else:
        start_date = today - timezone.timedelta(days=7)
        trunc_func = TruncDay
        date_format = '%d %b %Y'

    stock_data = StockMovement.objects.filter(
        timestamp__date__gte=start_date
    ).annotate(
        period=trunc_func('timestamp')
    ).values('period').annotate(
        stock_in=Sum('quantity', filter=Q(movement_type='IN')),
        stock_out=Sum('quantity', filter=Q(movement_type='OUT'))
    ).order_by('period')

    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = 'Laporan Stok'
    ws.append(['Periode', 'Stok Masuk', 'Stok Keluar'])
    for item in stock_data:
        period_str = item['period'].strftime(date_format)
        ws.append([period_str, item['stock_in'] or 0, item['stock_out'] or 0])
    for col in range(1, 4):
        ws.column_dimensions[get_column_letter(col)].width = 20
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=laporan_stok.xlsx'
    return response

# Export PDF

def export_stock_report_pdf(request):
    period = request.GET.get('period', 'daily')
    today = timezone.now().date()
    if period == 'weekly':
        start_date = today - timezone.timedelta(days=7)
        trunc_func = TruncWeek
        date_format = '%d %b %Y'
    elif period == 'monthly':
        start_date = today - timezone.timedelta(days=30)
        trunc_func = TruncMonth
        date_format = '%b %Y'
    else:
        start_date = today - timezone.timedelta(days=7)
        trunc_func = TruncDay
        date_format = '%d %b %Y'

    stock_data = StockMovement.objects.filter(
        timestamp__date__gte=start_date
    ).annotate(
        period=trunc_func('timestamp')
    ).values('period').annotate(
        stock_in=Sum('quantity', filter=Q(movement_type='IN')),
        stock_out=Sum('quantity', filter=Q(movement_type='OUT'))
    ).order_by('period')

    stock_rows = []
    for item in stock_data:
        period_str = item['period'].strftime(date_format)
        stock_rows.append({
            'period': period_str,
            'stock_in': item['stock_in'] or 0,
            'stock_out': item['stock_out'] or 0,
        })

    html_string = render_to_string('dashboard/reports/stock_report_pdf.html', {
        'period': period,
        'stock_rows': stock_rows,
    })
    result = io.BytesIO()
    pdf = pisa.pisaDocument(io.BytesIO(html_string.encode('UTF-8')), result)
    if not pdf.err:
        response = HttpResponse(result.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=laporan_stok.pdf'
        return response
    return HttpResponse('Gagal membuat PDF', status=500)
