{% extends "dashboard/base.html" %}
{% load humanize %}

{% block title %}Detail Produk{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Detail Produk</h1>
        <div>
            <a href="{% url 'dashboard:product_edit' product.id %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{% url 'dashboard:product_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Informasi Produk</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 30%">Kode</th>
                            <td>{{ product.code }}</td>
                        </tr>
                        <tr>
                            <th>Nama</th>
                            <td>{{ product.name }}</td>
                        </tr>
                        <tr>
                            <th>Kategori</th>
                            <td>{{ product.category|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th>Satuan</th>
                            <td>{{ product.unit.name|default:"-" }} ({{ product.unit.abbreviation|default:"-" }})</td>
                        </tr>
                        <tr>
                            <th>Deskripsi</th>
                            <td>{{ product.description|default:"-"|linebreaks }}</td>
                        </tr>
                        <tr>
                            <th>Ukuran</th>
                            <td>{{ product.get_size_display }}</td>
                        </tr>
                        <tr>
                            <th>Warna</th>
                            <td>{{ product.get_color_display }}</td>
                        </tr>
                        <tr>
                            <th style="width: 30%">Stok Saat Ini</th>
                            <td>
                                {% if product.quantity == 0 %}
                                <span class="badge badge-danger">
                                    <i class="fas fa-times-circle"></i> Habis
                                </span>
                                {% elif product.is_low_stock %}
                                <span class="badge badge-warning">
                                    <i class="fas fa-exclamation-triangle"></i> Rendah ({{ product.quantity }})
                                </span>
                                {% else %}
                                <span class="badge badge-success">
                                    <i class="fas fa-check-circle"></i> {{ product.quantity }}
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Notifikasi Terkait</h5>
                </div>
                <div class="card-body">
                    {% if product_notifications %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Tipe</th>
                                    <th>Pesan</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for notification in product_notifications %}
                                <tr>
                                    <td>{{ notification.created_at|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        {% if notification.type == 'low_stock' %}
                                        <span class="badge badge-warning">
                                            <i class="fas fa-exclamation-triangle"></i> Stok Rendah
                                        </span>
                                        {% elif notification.type == 'out_of_stock' %}
                                        <span class="badge badge-danger">
                                            <i class="fas fa-times-circle"></i> Stok Habis
                                        </span>
                                        {% elif notification.type == 'restock_reminder' %}
                                        <span class="badge badge-info">
                                            <i class="fas fa-sync-alt"></i> Pengingat Restock
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ notification.message }}</td>
                                    <td>
                                        {% if notification.status == 'unread' %}
                                        <span class="badge badge-warning">Belum Dibaca</span>
                                        {% elif notification.status == 'read' %}
                                        <span class="badge badge-success">Sudah Dibaca</span>
                                        {% else %}
                                        <span class="badge badge-secondary">Diabaikan</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state small">
                        <i class="fas fa-bell-slash"></i>
                        <p>Tidak ada notifikasi terkait produk ini.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Informasi Stok & Harga</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 30%">Stok Saat Ini</th>
                            <td>
                                {% if product.quantity == 0 %}
                                <span class="badge badge-danger">
                                    <i class="fas fa-times-circle"></i> Habis
                                </span>
                                {% elif product.is_low_stock %}
                                <span class="badge badge-warning">
                                    <i class="fas fa-exclamation-triangle"></i> Rendah ({{ product.quantity }})
                                </span>
                                {% else %}
                                <span class="badge badge-success">
                                    <i class="fas fa-check-circle"></i> {{ product.quantity }}
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Stok Minimum</th>
                            <td>{{ product.min_stock_level }}</td>
                        </tr>
                        <tr>
                            <th>Harga Beli</th>
                            <td>Rp {{ product.purchase_price|floatformat:0 }}</td>
                        </tr>
                        <tr>
                            <th>Harga Jual</th>
                            <td>Rp {{ product.selling_price|floatformat:0 }}</td>
                        </tr>
                        <tr>
                            <th>Margin Keuntungan</th>
                            <td>{{ product.profit_margin|floatformat:2 }}%</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Aksi Cepat</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'dashboard:stock_in' %}?product={{ product.id }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Tambah Stok
                        </a>
                        <a href="{% url 'dashboard:stock_out' %}?product={{ product.id }}" class="btn btn-danger">
                            <i class="fas fa-minus"></i> Kurangi Stok
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Riwayat Pergerakan Stok</h5>
            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                <i class="fas fa-filter"></i> Filter
            </button>
        </div>
        
        <div class="collapse" id="filterCollapse">
            <div class="card-body bg-light">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Tipe Pergerakan</label>
                        <select name="movement_type" class="form-select">
                            <option value="">Semua</option>
                            <option value="IN" {% if movement_type == 'IN' %}selected{% endif %}>Stok Masuk</option>
                            <option value="OUT" {% if movement_type == 'OUT' %}selected{% endif %}>Stok Keluar</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Dari Tanggal</label>
                        <input type="date" name="date_from" class="form-control" value="{{ date_from|date:'Y-m-d' }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Sampai Tanggal</label>
                        <input type="date" name="date_to" class="form-control" value="{{ date_to|date:'Y-m-d' }}">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Terapkan Filter
                        </button>
                        <a href="{% url 'dashboard:product_detail' product.id %}" class="btn btn-secondary">
                            <i class="fas fa-sync"></i> Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Tanggal</th>
                        <th>Tipe</th>
                        <th>Jumlah</th>
                        <th>Catatan</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movement in stock_movements %}
                    <tr>
                        <td>{{ movement.timestamp|date:"d/m/Y H:i" }}</td>
                        <td>
                            {% if movement.movement_type == 'IN' %}
                            <span class="badge bg-success">Masuk</span>
                            {% else %}
                            <span class="badge bg-danger">Keluar</span>
                            {% endif %}
                        </td>
                        <td>{{ movement.quantity }}</td>
                        <td>{{ movement.notes|default:"-" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="text-center">Tidak ada riwayat pergerakan stok</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}





