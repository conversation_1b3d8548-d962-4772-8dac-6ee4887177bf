{% extends "dashboard/base.html" %}
{% load humanize %}

{% block title %}Dashboard{% endblock %}

{% block head_extras %}
<style>
    /* Styling untuk dashboard */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .page-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }
    
    /* Statistik cards */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        border-radius: 8px;
        padding: 15px;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
    
    .stat-card.blue {
        background-color: #3498db;
        border-left: 5px solid #2980b9;
    }
    
    .stat-card.green {
        background-color: #2ecc71;
        border-left: 5px solid #27ae60;
    }
    
    .stat-card.yellow {
        background-color: #f39c12;
        border-left: 5px solid #e67e22;
    }
    
    .stat-card.red {
        background-color: #e74c3c;
        border-left: 5px solid #c0392b;
    }
    
    .stat-icon {
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }
    
    .stat-icon i {
        font-size: 24px;
    }
    
    .stat-info h5 {
        font-size: 14px;
        margin: 0 0 5px 0;
        opacity: 0.8;
    }
    
    .stat-info h2 {
        font-size: 28px;
        margin: 0;
        font-weight: bold;
    }
    
    /* Card styling */
    .card-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .card-header {
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .card-header h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
    }
    
    .card-body {
        padding: 20px;
    }
    
    /* Alert items */
    .alert-item {
        padding: 12px 15px;
        border-left: 3px solid;
        margin-bottom: 10px;
        background-color: #f8f9fa;
        border-radius: 4px;
        transition: all 0.2s;
    }
    
    .alert-item:hover {
        transform: translateX(5px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .alert-item.warning {
        border-left-color: #f39c12;
    }
    
    .alert-item.danger {
        border-left-color: #e74c3c;
    }
    
    .alert-item.info {
        border-left-color: #3498db;
    }
    
    /* Table styling */
    .table-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .data-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .data-table th {
        background-color: #f8f9fa;
        padding: 12px 15px;
        text-align: left;
        font-weight: 600;
        font-size: 13px;
        color: #555;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .data-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .data-table tr:last-child td {
        border-bottom: none;
    }
    
    .data-table tr:hover {
        background-color: #f5f5f5;
    }
    
    /* Badge styling */
    .badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .badge-danger {
        background-color: #e74c3c;
        color: white;
    }
    
    .badge-warning {
        background-color: #f39c12;
        color: white;
    }
    
    .badge-success {
        background-color: #2ecc71;
        color: white;
    }
    
    /* Chart container */
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .stats-container {
            grid-template-columns: 1fr;
        }
        
        .card-container {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Header -->
    <div class="page-header">
        <h1 class="page-title">Dashboard</h1>
    </div>
    
    <!-- Statistik -->
    <div class="stats-container">
        <div class="stat-card blue">
            <div class="stat-icon">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-info">
                <h5>Total Produk</h5>
                <h2>{{ total_products }}</h2>
            </div>
        </div>
        <div class="stat-card green">
            <div class="stat-icon">
                <i class="fas fa-cubes"></i>
            </div>
            <div class="stat-info">
                <h5>Total Stok</h5>
                <h2>{{ total_stock }}</h2>
            </div>
        </div>
        <div class="stat-card yellow">
            <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-info">
                <h5>Stok Rendah</h5>
                <h2>{{ low_stock_products }}</h2>
            </div>
        </div>
        <div class="stat-card blue">
            <div class="stat-icon">
                <i class="fas fa-sync-alt"></i>
            </div>
            <div class="stat-info">
                <h5>Pengingat Restock</h5>
                <h2>{{ restock_reminder_count }}</h2>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Grafik Pergerakan Stok -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Pergerakan Stok</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="stockMovementChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Peringatan -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Peringatan</h5>
                    <a href="{% url 'dashboard:notification_list' %}" class="btn btn-sm btn-primary">Lihat Semua Notifikasi</a>
                </div>
                <div class="card-body">
                    {% if low_stock_items %}
                        {% for product in low_stock_items %}
                        <div class="alert-item {% if product.quantity == 0 %}danger{% else %}warning{% endif %}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ product.name }}</h6>
                                    <p class="mb-0 small">
                                        {% if product.quantity == 0 %}
                                        <span class="badge badge-danger">
                                            <i class="fas fa-times-circle"></i> Stok Habis
                                        </span>
                                        {% else %}
                                        <span class="badge badge-warning">
                                            <i class="fas fa-exclamation-triangle"></i> Stok Rendah
                                        </span>
                                        {% endif %}
                                    </p>
                                </div>
                                <a href="{% url 'dashboard:product_detail' product.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <p>Tidak ada peringatan stok saat ini.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Produk Terbaru -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Produk Terbaru</h5>
            <a href="{% url 'dashboard:product_list' %}" class="btn btn-sm btn-primary">Lihat Semua</a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Kode</th>
                            <th>Nama Produk</th>
                            <th>Kategori</th>
                            <th>Stok</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in recent_products %}
                        <tr>
                            <td>{{ product.code }}</td>
                            <td>{{ product.name }}</td>
                            <td>{{ product.category.name|default:"-" }}</td>
                            <td>{{ product.quantity }} {{ product.unit.abbreviation }}</td>
                            <td>
                                {% if product.quantity == 0 %}
                                <span class="badge badge-danger">Stok Habis</span>
                                {% elif product.quantity <= product.min_stock_level %}
                                <span class="badge badge-warning">Stok Rendah</span>
                                {% else %}
                                <span class="badge badge-success">Tersedia</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'dashboard:product_detail' product.id %}" class="btn btn-sm btn-outline-primary">Detail</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">Belum ada produk.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Grafik pergerakan stok
        const ctx = document.getElementById('stockMovementChart').getContext('2d');
        const stockMovementChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ labels|safe }},
                datasets: [
                    {
                        label: 'Barang Masuk',
                        data: {{ stock_in_data|safe }},
                        backgroundColor: 'rgba(52, 152, 219, 0.5)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: 'rgba(52, 152, 219, 1)',
                        pointRadius: 4,
                        pointHoverRadius: 6
                    },
                    {
                        label: 'Barang Keluar',
                        data: {{ stock_out_data|safe }},
                        backgroundColor: 'rgba(231, 76, 60, 0.5)',
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: 'rgba(231, 76, 60, 1)',
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>
{% endblock %}








