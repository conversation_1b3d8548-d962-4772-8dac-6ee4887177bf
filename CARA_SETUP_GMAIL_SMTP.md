# 📧 Cara Setup Gmail SMTP untuk Email Notifikasi

## ⚠️ MASALAH SAAT INI
Error: `Application-specific password required`

<PERSON>i berarti App Password Gmail belum dibuat dengan benar atau 2FA belum diaktifkan.

## 🔧 LANGKAH-LANGKAH SETUP GMAIL SMTP

### 1. Aktifkan 2-Factor Authentication (2FA)

1. **Buka Google Account Security**: https://myaccount.google.com/security
2. **Login** dengan akun Gmail Anda (<EMAIL>)
3. **Cari "2-Step Verification"** di halaman Security
4. **<PERSON><PERSON> "Get started"** atau "Turn on" jika belum aktif
5. **Ikuti langkah-langkah** untuk setup 2FA (biasanya dengan nomor HP)
6. **Pastikan 2FA aktif** (akan ada tanda centang hijau)

### 2. Buat App Password

1. **Masih di halaman Security**, scroll ke bawah
2. **<PERSON>i "App passwords"** (mungkin perlu scroll atau cari)
3. **<PERSON><PERSON> "App passwords"**
4. **Pilih app**: "Mail" atau "Other (custom name)"
5. **Jika pilih Other**, ketik: "Stock Manager"
6. **Klik "Generate"**
7. **SALIN App Password** yang muncul (16 karakter, contoh: `abcd efgh ijkl mnop`)
8. **SIMPAN** App Password ini dengan aman

### 3. Update Konfigurasi

Setelah mendapat App Password yang benar, jalankan:

```bash
python manage.py setup_email_smtp --gmail
```

Masukkan:
- **Email**: <EMAIL>
- **App Password**: [16 karakter yang baru dibuat]

### 4. Test Koneksi

```bash
python manage.py setup_email_smtp --test-smtp
```

## 🚨 TROUBLESHOOTING

### Jika 2FA Sudah Aktif tapi App Password Tidak Muncul

1. **Logout** dari Google Account
2. **Login kembali**
3. **Buka**: https://myaccount.google.com/apppasswords
4. **Atau cari "App passwords"** di Google Account settings

### Jika Masih Error "Application-specific password required"

1. **Pastikan menggunakan App Password**, bukan password Gmail biasa
2. **App Password harus 16 karakter** tanpa spasi
3. **Coba buat App Password baru** jika yang lama tidak bekerja
4. **Pastikan akun Gmail tidak menggunakan Advanced Protection**

### Jika Error "Less secure app access"

Gmail sudah tidak mendukung "Less secure app access" sejak 2022. **HARUS menggunakan App Password**.

## 🔄 ALTERNATIF SEMENTARA

Jika Gmail SMTP sulit disetup, bisa menggunakan email service lain:

### 1. Outlook/Hotmail SMTP
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp-mail.outlook.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

### 2. Yahoo SMTP
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.mail.yahoo.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
```

## 📝 LANGKAH SELANJUTNYA

1. **Setup 2FA Gmail** jika belum
2. **Buat App Password Gmail** yang benar
3. **Update konfigurasi** dengan App Password baru
4. **Test koneksi SMTP**
5. **Kirim email notifikasi**

## 🆘 JIKA MASIH BERMASALAH

Bisa menggunakan email service yang lebih mudah atau tetap menggunakan console backend untuk development.

Console backend sudah berfungsi dengan baik untuk testing, hanya saja email muncul di terminal bukan dikirim ke email sebenarnya.

## 📞 BANTUAN LEBIH LANJUT

Jika masih ada masalah, bisa:
1. Coba email provider lain (Outlook, Yahoo)
2. Gunakan email service seperti SendGrid, Mailgun
3. Tetap gunakan console backend untuk development
4. Setup SMTP server sendiri

## ✅ VERIFIKASI BERHASIL

Jika setup berhasil, akan muncul:
```
✅ Koneksi SMTP berhasil
✅ Email test berhasil dikirim
📧 Cek inbox email: [daftar email penerima]
```
