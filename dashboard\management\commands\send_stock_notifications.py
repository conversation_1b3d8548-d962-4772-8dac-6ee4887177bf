from django.core.management.base import BaseCommand
from dashboard.utils import send_low_stock_email_notification, get_admin_and_owner_emails
from dashboard.models import Product
from django.db.models import F

class Command(BaseCommand):
    help = 'Mengirim email notifikasi untuk produk dengan stok rendah atau habis'

    def add_arguments(self, parser):
        parser.add_argument(
            '--batch',
            action='store_true',
            help='Kirim email batch untuk semua produk stok rendah/habis',
        )

    def handle(self, *args, **options):
        self.stdout.write('Mengirim email notifikasi stok rendah...')

        # Cek apakah ada admin/owner yang bisa menerima email
        recipient_emails = get_admin_and_owner_emails()
        if not recipient_emails:
            self.stdout.write(
                self.style.WARNING('Tidak ada admin atau owner dengan email yang valid untuk menerima notifikasi.')
            )
            return

        if options['batch']:
            # Kirim email batch untuk semua produk
            send_low_stock_email_notification()
            self.stdout.write(
                self.style.SUCCESS(f'Email notifikasi batch berhasil dikirim ke {len(recipient_emails)} penerima.')
            )
        else:
            # Kirim email individual untuk setiap produk yang stok rendah/habis
            low_stock_products = Product.objects.filter(quantity__lte=F('min_stock_level'), quantity__gt=0)
            out_of_stock_products = Product.objects.filter(quantity=0)

            total_sent = 0

            # Kirim untuk produk stok rendah
            for product in low_stock_products:
                send_low_stock_email_notification(product=product)
                total_sent += 1

            # Kirim untuk produk stok habis
            for product in out_of_stock_products:
                send_low_stock_email_notification(product=product)
                total_sent += 1

            if total_sent > 0:
                self.stdout.write(
                    self.style.SUCCESS(f'Email notifikasi berhasil dikirim untuk {total_sent} produk ke {len(recipient_emails)} penerima.')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('Tidak ada produk yang memerlukan notifikasi stok.')
                )