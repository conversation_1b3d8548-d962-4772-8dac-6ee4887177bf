from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.conf import settings
from dashboard.utils import get_admin_and_owner_emails, send_low_stock_email_notification
from dashboard.models import Product, Notification
from django.db.models import F
import traceback

class Command(BaseCommand):
    help = 'Diagnosa dan test sistem email notifikasi stok rendah'

    def add_arguments(self, parser):
        parser.add_argument(
            '--send-test-email',
            action='store_true',
            help='Kirim email test langsung ke semua admin/owner',
        )
        parser.add_argument(
            '--force-send',
            action='store_true',
            help='Paksa kirim email untuk semua produk stok rendah/habis',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== DIAGNOSA SISTEM EMAIL NOTIFIKASI ===\n'))
        
        # Step 1: Cek konfiguras<PERSON> email
        self.check_email_settings()
        
        # Step 2: Cek user admin/owner
        self.check_users()
        
        # Step 3: Cek produk stok rendah/habis
        self.check_products()
        
        # Step 4: Cek notifikasi yang ada
        self.check_notifications()
        
        if options['send_test_email']:
            self.send_test_email()
        
        if options['force_send']:
            self.force_send_notifications()

    def check_email_settings(self):
        self.stdout.write(self.style.WARNING('[1] KONFIGURASI EMAIL'))
        self.stdout.write('-' * 40)
        
        backend = getattr(settings, 'EMAIL_BACKEND', 'Tidak diset')
        self.stdout.write(f'EMAIL_BACKEND: {backend}')
        
        if 'console' in backend.lower():
            self.stdout.write(self.style.SUCCESS('✅ Menggunakan Console Backend - Email akan muncul di terminal'))
        elif 'smtp' in backend.lower():
            host = getattr(settings, 'EMAIL_HOST', 'Tidak diset')
            port = getattr(settings, 'EMAIL_PORT', 'Tidak diset')
            use_tls = getattr(settings, 'EMAIL_USE_TLS', 'Tidak diset')
            user = getattr(settings, 'EMAIL_HOST_USER', 'Tidak diset')
            self.stdout.write(f'EMAIL_HOST: {host}')
            self.stdout.write(f'EMAIL_PORT: {port}')
            self.stdout.write(f'EMAIL_USE_TLS: {use_tls}')
            self.stdout.write(f'EMAIL_HOST_USER: {user}')
        
        default_from = getattr(settings, 'DEFAULT_FROM_EMAIL', 'Tidak diset')
        self.stdout.write(f'DEFAULT_FROM_EMAIL: {default_from}')

    def check_users(self):
        self.stdout.write(self.style.WARNING('\n[2] USER ADMIN DAN OWNER'))
        self.stdout.write('-' * 40)
        
        # Cek semua user
        all_users = User.objects.all()
        self.stdout.write(f'Total user: {all_users.count()}')
        
        # Cek admin users
        admin_users = User.objects.filter(is_staff=True, is_superuser=False)
        self.stdout.write(f'\nAdmin users ({admin_users.count()}):')
        for user in admin_users:
            email_status = user.email if user.email else '❌ TIDAK ADA EMAIL'
            active_status = '✅ AKTIF' if user.is_active else '❌ TIDAK AKTIF'
            self.stdout.write(f'  - {user.username}: {email_status} ({active_status})')
        
        # Cek owner users
        owner_users = User.objects.filter(is_superuser=True)
        self.stdout.write(f'\nOwner users ({owner_users.count()}):')
        for user in owner_users:
            email_status = user.email if user.email else '❌ TIDAK ADA EMAIL'
            active_status = '✅ AKTIF' if user.is_active else '❌ TIDAK AKTIF'
            self.stdout.write(f'  - {user.username}: {email_status} ({active_status})')
        
        # Cek email yang akan menerima notifikasi
        recipient_emails = get_admin_and_owner_emails()
        self.stdout.write(f'\n📧 Email penerima notifikasi ({len(recipient_emails)}):')
        if recipient_emails:
            for email in recipient_emails:
                self.stdout.write(f'  ✅ {email}')
        else:
            self.stdout.write(self.style.ERROR('  ❌ TIDAK ADA EMAIL PENERIMA YANG VALID!'))
            self.stdout.write('     Solusi: Pastikan admin/owner memiliki email dan akun aktif')

    def check_products(self):
        self.stdout.write(self.style.WARNING('\n[3] PRODUK STOK RENDAH/HABIS'))
        self.stdout.write('-' * 40)
        
        # Cek produk stok rendah
        low_stock_products = Product.objects.filter(quantity__lte=F('min_stock_level'), quantity__gt=0)
        self.stdout.write(f'Produk stok rendah: {low_stock_products.count()}')
        for product in low_stock_products[:5]:  # Tampilkan 5 pertama
            self.stdout.write(f'  📦 {product.name}: {product.quantity}/{product.min_stock_level}')
        
        # Cek produk stok habis
        out_of_stock_products = Product.objects.filter(quantity=0)
        self.stdout.write(f'Produk stok habis: {out_of_stock_products.count()}')
        for product in out_of_stock_products[:5]:  # Tampilkan 5 pertama
            self.stdout.write(f'  📦 {product.name}: {product.quantity}')
        
        if not low_stock_products.exists() and not out_of_stock_products.exists():
            self.stdout.write(self.style.SUCCESS('  ✅ Tidak ada produk yang memerlukan notifikasi'))

    def check_notifications(self):
        self.stdout.write(self.style.WARNING('\n[4] NOTIFIKASI YANG ADA'))
        self.stdout.write('-' * 40)
        
        # Cek notifikasi stok rendah
        low_stock_notifications = Notification.objects.filter(type='low_stock', status__in=['unread', 'read'])
        self.stdout.write(f'Notifikasi stok rendah aktif: {low_stock_notifications.count()}')
        
        # Cek notifikasi stok habis
        out_of_stock_notifications = Notification.objects.filter(type='out_of_stock', status__in=['unread', 'read'])
        self.stdout.write(f'Notifikasi stok habis aktif: {out_of_stock_notifications.count()}')
        
        # Tampilkan beberapa notifikasi terbaru
        recent_notifications = Notification.objects.filter(
            type__in=['low_stock', 'out_of_stock']
        ).order_by('-created_at')[:5]
        
        if recent_notifications.exists():
            self.stdout.write('\nNotifikasi terbaru:')
            for notif in recent_notifications:
                status_icon = '🔴' if notif.status == 'unread' else '🟡' if notif.status == 'read' else '⚪'
                self.stdout.write(f'  {status_icon} {notif.product.name} - {notif.get_type_display()} ({notif.created_at.strftime("%Y-%m-%d %H:%M")})')

    def send_test_email(self):
        self.stdout.write(self.style.WARNING('\n[5] KIRIM EMAIL TEST'))
        self.stdout.write('-' * 40)
        
        recipient_emails = get_admin_and_owner_emails()
        if not recipient_emails:
            self.stdout.write(self.style.ERROR('❌ Tidak dapat mengirim email: tidak ada penerima yang valid'))
            return
        
        try:
            # Kirim email test sederhana
            self.stdout.write('📧 Mengirim email test sederhana...')
            send_mail(
                subject='[Stock Manager] Test Email Notifikasi',
                message='Ini adalah email test dari sistem Stock Manager.\n\nJika Anda menerima email ini, berarti sistem email berfungsi dengan baik.',
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=recipient_emails,
                fail_silently=False,
            )
            self.stdout.write(self.style.SUCCESS(f'✅ Email test berhasil dikirim ke {len(recipient_emails)} penerima'))
            
            # Kirim email dengan template stok rendah
            self.stdout.write('\n📧 Mengirim email test dengan template stok rendah...')
            send_low_stock_email_notification()
            self.stdout.write(self.style.SUCCESS('✅ Email template stok rendah berhasil dikirim'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error mengirim email: {e}'))
            self.stdout.write(f'Detail error: {traceback.format_exc()}')

    def force_send_notifications(self):
        self.stdout.write(self.style.WARNING('\n[6] PAKSA KIRIM NOTIFIKASI'))
        self.stdout.write('-' * 40)
        
        recipient_emails = get_admin_and_owner_emails()
        if not recipient_emails:
            self.stdout.write(self.style.ERROR('❌ Tidak dapat mengirim email: tidak ada penerima yang valid'))
            return
        
        # Kirim untuk produk stok rendah
        low_stock_products = Product.objects.filter(quantity__lte=F('min_stock_level'), quantity__gt=0)
        for product in low_stock_products:
            try:
                self.stdout.write(f'📧 Mengirim email untuk {product.name} (stok rendah)...')
                send_low_stock_email_notification(product=product)
                self.stdout.write(self.style.SUCCESS(f'  ✅ Berhasil'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  ❌ Error: {e}'))
        
        # Kirim untuk produk stok habis
        out_of_stock_products = Product.objects.filter(quantity=0)
        for product in out_of_stock_products:
            try:
                self.stdout.write(f'📧 Mengirim email untuk {product.name} (stok habis)...')
                send_low_stock_email_notification(product=product)
                self.stdout.write(self.style.SUCCESS(f'  ✅ Berhasil'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  ❌ Error: {e}'))
        
        total_products = low_stock_products.count() + out_of_stock_products.count()
        if total_products > 0:
            self.stdout.write(self.style.SUCCESS(f'\n✅ Selesai mengirim email untuk {total_products} produk'))
        else:
            self.stdout.write(self.style.SUCCESS('\n✅ Tidak ada produk yang memerlukan notifikasi'))
