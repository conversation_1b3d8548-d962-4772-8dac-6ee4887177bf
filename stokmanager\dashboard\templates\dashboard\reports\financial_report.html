{% extends "dashboard/base.html" %}
{% load humanize %}
{% block title %}<PERSON><PERSON><PERSON>{% endblock %}

{% block head_extras %}
<style>
    /* Styling untuk laporan keuangan */
    .report-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .report-header {
        margin-bottom: 20px;
    }
    
    .report-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .report-subtitle {
        font-size: 16px;
        color: #6c757d;
        margin-bottom: 20px;
    }
    
    .filter-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        padding: 15px 20px;
    }
    
    .chart-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .chart-header {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .chart-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }
    
    .chart-body {
        padding: 20px;
        position: relative;
        min-height: 300px;
    }
    
    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .summary-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        padding: 20px;
        display: flex;
        align-items: center;
        transition: transform 0.3s, box-shadow 0.3s;
    }
    
    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
    
    .summary-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
    }
    
    .summary-icon.capital {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .summary-icon.sales {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }
    
    .summary-icon.profit {
        background-color: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }
    
    .summary-content {
        flex: 1;
    }
    
    .summary-title {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 5px;
    }
    
    .summary-value {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 0;
    }
    
    .btn-export {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-export:hover {
        background-color: #218838;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="report-container">
        <div class="report-header">
            <h1 class="report-title">Laporan Keuangan</h1>
            <p class="report-subtitle">Analisis pendapatan, pengeluaran, dan keuntungan</p>
        </div>
        
        <!-- Filter -->
        <div class="filter-card">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="period" class="form-label">Periode</label>
                    <select name="period" id="period" class="form-select" onchange="this.form.submit()">
                        <option value="daily" {% if period == 'daily' %}selected{% endif %}>Harian</option>
                        <option value="weekly" {% if period == 'weekly' %}selected{% endif %}>Mingguan</option>
                        <option value="monthly" {% if period == 'monthly' %}selected{% endif %}>Bulanan</option>
                    </select>
                </div>
            </form>
        </div>
        
        <!-- Ringkasan Keuangan -->
        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-icon capital">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="summary-content">
                    <h5 class="summary-title">Total Modal</h5>
                    <p class="summary-value">Rp {{ summary.total_purchase|floatformat:0|intcomma }}</p>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon sales">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="summary-content">
                    <h5 class="summary-title">Total Penjualan</h5>
                    <p class="summary-value">Rp {{ summary.total_sales|floatformat:0|intcomma }}</p>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon profit">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="summary-content">
                    <h5 class="summary-title">Total Keuntungan</h5>
                    <p class="summary-value">Rp {{ summary.total_profit|floatformat:0|intcomma }}</p>
                </div>
            </div>
        </div>
        
        <!-- Grafik Keuangan -->
        <div class="chart-card">
            <div class="chart-header">
                <h5 class="chart-title">Grafik Keuangan</h5>
                <button class="btn-export" onclick="exportPDF()">
                    <i class="fas fa-file-pdf"></i> Export PDF
                </button>
            </div>
            <div class="chart-body">
                <canvas id="financialChart" height="300"></canvas>
            </div>
        </div>
        
        <!-- Grafik Keuntungan -->
        <div class="chart-card">
            <div class="chart-header">
                <h5 class="chart-title">Grafik Keuntungan</h5>
            </div>
            <div class="chart-body">
                <canvas id="profitChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // Data untuk grafik
            const periods = {{ periods_json|safe }};
            const purchaseData = {{ purchase_json|safe }};
            const salesData = {{ sales_json|safe }};
            const profitData = {{ profit_json|safe }};
            
            // Grafik Keuangan
            const financialChartElement = document.getElementById('financialChart');
            if (financialChartElement) {
                const ctxFinancial = financialChartElement.getContext('2d');
                const financialChart = new Chart(ctxFinancial, {
                    type: 'line',
                    data: {
                        labels: periods,
                        datasets: [
                            {
                                label: 'Modal',
                                data: purchaseData,
                                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 2,
                                tension: 0.4,
                                fill: true,
                                pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                                pointRadius: 4,
                                pointHoverRadius: 6
                            },
                            {
                                label: 'Penjualan',
                                data: salesData,
                                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 2,
                                tension: 0.4,
                                fill: true,
                                pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                                pointRadius: 4,
                                pointHoverRadius: 6
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            }
                        }
                    }
                });
            }
            
            // Grafik Keuntungan
            const profitChartElement = document.getElementById('profitChart');
            if (profitChartElement) {
                const ctxProfit = profitChartElement.getContext('2d');
                const profitChart = new Chart(ctxProfit, {
                    type: 'line',
                    data: {
                        labels: periods,
                        datasets: [
                            {
                                label: 'Keuntungan',
                                data: profitData,
                                backgroundColor: 'rgba(255, 159, 64, 0.5)',
                                borderColor: 'rgba(255, 159, 64, 1)',
                                borderWidth: 2,
                                tension: 0.4,
                                fill: true,
                                pointBackgroundColor: 'rgba(255, 159, 64, 1)',
                                pointRadius: 4,
                                pointHoverRadius: 6
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            }
                        }
                    }
                });
            }
        } catch (error) {
            console.error("Error initializing charts:", error);
        }
    });
    
    // Fungsi untuk export PDF
    function exportPDF() {
        // Pastikan jsPDF tersedia
        if (typeof window.jspdf === 'undefined') {
            window.jspdf = window.jspdf || {};
        }
        
        if (typeof window.jspdf.jsPDF === 'undefined') {
            window.jspdf.jsPDF = window.jsPDF;
        }
        
        const { jsPDF } = window.jspdf;
        
        // Buat dokumen PDF
        const doc = new jsPDF('landscape', 'mm', 'a4');
        
        // Tambahkan judul
        doc.setFontSize(18);
        doc.text('Laporan Keuangan', 14, 20);
        
        // Tambahkan tanggal
        const today = new Date();
        const dateStr = today.toLocaleDateString('id-ID', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
        doc.setFontSize(12);
        doc.text(`Tanggal: ${dateStr}`, 14, 30);
        
        // Tambahkan ringkasan keuangan
        doc.setFontSize(14);
        doc.text('Ringkasan Keuangan', 14, 40);
        
        const totalPurchase = document.querySelector('.summary-card:nth-child(1) .summary-value').textContent;
        const totalSales = document.querySelector('.summary-card:nth-child(2) .summary-value').textContent;
        const totalProfit = document.querySelector('.summary-card:nth-child(3) .summary-value').textContent;
        
        doc.setFontSize(12);
        doc.text(`Total Modal: ${totalPurchase}`, 14, 50);
        doc.text(`Total Penjualan: ${totalSales}`, 14, 58);
        doc.text(`Total Keuntungan: ${totalProfit}`, 14, 66);
        
        // Tambahkan grafik
        const financialChart = Chart.getChart('financialChart');
        const profitChart = Chart.getChart('profitChart');
        
        if (financialChart) {
            doc.setFontSize(14);
            doc.text('Grafik Keuangan', 14, 80);
            
            // Konversi grafik ke gambar
            const financialChartImage = financialChart.toBase64Image();
            doc.addImage(financialChartImage, 'PNG', 14, 85, 270, 80);
        }
        
        if (profitChart) {
            doc.setFontSize(14);
            doc.text('Grafik Keuntungan', 14, 180);
            
            // Konversi grafik ke gambar
            const profitChartImage = profitChart.toBase64Image();
            doc.addImage(profitChartImage, 'PNG', 14, 185, 270, 80);
        }
        
        // Simpan PDF
        doc.save('laporan-keuangan.pdf');
    }
</script>
{% endblock %}







