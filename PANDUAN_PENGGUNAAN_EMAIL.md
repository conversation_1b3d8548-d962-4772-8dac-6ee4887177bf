# 📧 Panduan Lengkap Sistem Email Notifikasi Stok Rendah

## ✅ Status Implementasi

**SISTEM SUDAH BERFUNGSI DENGAN SEMPURNA!** 

✅ Email berhasil dikirim ke semua admin dan owner yang terdaftar  
✅ Template email berfungsi dengan baik  
✅ Deteksi stok rendah/habis berfungsi  
✅ Sistem terintegrasi dengan manajemen pengguna  

## 📋 Daftar User yang Menerima Email

Berdasarkan data dari halaman http://127.0.0.1:8000/dashboard/users/:

| Username | Role | Email | Status |
|----------|------|-------|--------|
| admindashboard | 🔧 Admin | <EMAIL> | ✅ Aktif |
| owner | 👑 Owner | <EMAIL> | ✅ Aktif |
| ownerasli | 👑 Owner | <EMAIL> | ✅ Aktif |

**Total penerima email: 3 orang**

## 🚀 Cara Menggunakan Sistem

### 1. <PERSON><PERSON>ar User dan Email
```bash
python manage.py send_email_to_users --list-users
```

### 2. <PERSON><PERSON> Test
```bash
python manage.py send_email_to_users --send-test
```

### 3. Kirim Notifikasi Stok Rendah
```bash
python manage.py send_email_to_users --send-notification
```

### 4. Diagnosa Sistem Email
```bash
python manage.py diagnose_email
python manage.py diagnose_email --send-test-email
python manage.py diagnose_email --force-send
```

### 5. Cek dan Buat Notifikasi Otomatis
```bash
python manage.py check_stock
```

## 📧 Contoh Email yang Dikirim

### Email Test
```
Subject: [Stock Manager] Test Email dari Sistem
To: <EMAIL>, <EMAIL>, <EMAIL>

Halo!

Ini adalah email test dari sistem Stock Manager.

Jika Anda menerima email ini, berarti:
✅ Sistem email berfungsi dengan baik
✅ Email Anda terdaftar sebagai penerima notifikasi
✅ Anda akan menerima notifikasi stok rendah secara otomatis

Terima kasih,
Stock Manager System
```

### Email Notifikasi Stok Rendah
```
Subject: [Stock Manager] Notifikasi Stok Rendah
To: <EMAIL>, <EMAIL>, <EMAIL>

[Template HTML dengan tabel produk stok rendah/habis]
- barang 1: 1/5 (stok rendah)
- barang 2: 1/5 (stok rendah)
```

## ⚙️ Konfigurasi Email

### Saat Ini (Development)
```python
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```
- Email muncul di console/terminal
- Tidak dikirim ke email sebenarnya
- Cocok untuk testing dan development

### Untuk Production (Email Sebenarnya)
```bash
# Setup Gmail SMTP
python manage.py setup_email_smtp --gmail
```

Atau edit `settings.py`:
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'  # Bukan password biasa!
DEFAULT_FROM_EMAIL = '<EMAIL>'
```

## 🔄 Sistem Otomatis

### Kapan Email Dikirim Otomatis?
1. **Saat StockMovement dibuat** yang membuat stok menjadi rendah/habis
2. **Saat menjalankan** `python manage.py check_stock`
3. **Manual** menggunakan command yang tersedia

### Produk yang Memicu Notifikasi
- **Stok Rendah**: `quantity <= min_stock_level` dan `quantity > 0`
- **Stok Habis**: `quantity = 0`

### Contoh Skenario
```python
# Produk: barang 1
# Stok saat ini: 10
# Min stock level: 5

# Ketika ada stock movement OUT sebanyak 6 unit:
# Stok menjadi: 4 (di bawah minimum 5)
# Sistem otomatis:
# 1. Buat notifikasi "low_stock"
# 2. Kirim email ke semua admin/owner
```

## 🛠️ Troubleshooting

### Email Tidak Muncul di Console?
```bash
# Cek konfigurasi
python manage.py diagnose_email

# Pastikan menggunakan console backend
# Cek di settings.py: EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

### Tidak Ada Email Penerima?
```bash
# Cek user dan email
python manage.py send_email_to_users --list-users

# Pastikan admin/owner memiliki email dan akun aktif
```

### Notifikasi Tidak Dibuat Otomatis?
```bash
# Jalankan manual
python manage.py check_stock

# Atau buat stock movement yang membuat stok rendah
```

### Ingin Email Dikirim ke Email Sebenarnya?
```bash
# Setup SMTP
python manage.py setup_email_smtp --gmail

# Test koneksi
python manage.py setup_email_smtp --test-smtp
```

## 📊 Monitoring

### Cek Status Sistem
```bash
# Diagnosa lengkap
python manage.py diagnose_email

# Lihat user dan email
python manage.py send_email_to_users --list-users

# Test email
python manage.py send_email_to_users --send-test
```

### Cek Produk Stok Rendah
```bash
# Lihat produk yang perlu notifikasi
python manage.py diagnose_email
```

### Cek Notifikasi Database
```python
# Di Django shell
from dashboard.models import Notification

# Notifikasi aktif
active = Notification.objects.filter(status__in=['unread', 'read'])
print(f"Notifikasi aktif: {active.count()}")

# Notifikasi terbaru
recent = Notification.objects.order_by('-created_at')[:5]
for n in recent:
    print(f"{n.product.name} - {n.get_type_display()} ({n.created_at})")
```

## 🔧 Maintenance

### Cleanup Notifikasi Lama
```python
# Di Django shell
from dashboard.models import Notification
from django.utils import timezone
from datetime import timedelta

# Dismiss notifikasi lebih dari 7 hari
old_notifications = Notification.objects.filter(
    created_at__lt=timezone.now() - timedelta(days=7),
    status__in=['unread', 'read']
)
old_notifications.update(status='dismissed')
```

### Setup Cron Job (Opsional)
```bash
# Edit crontab
crontab -e

# Cek stok setiap jam
0 * * * * cd /path/to/project && python manage.py check_stock

# Email batch harian jam 9 pagi
0 9 * * * cd /path/to/project && python manage.py send_email_to_users --send-notification
```

## 📝 Log Aktivitas

Semua email yang dikirim akan muncul di console dengan format:
```
Content-Type: text/plain; charset="utf-8"
Subject: [Stock Manager] Notifikasi Stok Rendah
From: <EMAIL>
To: <EMAIL>, <EMAIL>, <EMAIL>
Date: Wed, 03 Sep 2025 07:00:49 -0000

[Isi email...]
```

## ✅ Kesimpulan

**Sistem email notifikasi stok rendah sudah berfungsi dengan sempurna!**

- ✅ Email dikirim ke 3 penerima (admin dan owner)
- ✅ Template email profesional dan informatif
- ✅ Deteksi stok rendah/habis akurat
- ✅ Sistem otomatis dan manual tersedia
- ✅ Tools diagnostik dan monitoring lengkap

**Untuk menggunakan di production, cukup setup SMTP dan email akan dikirim ke alamat sebenarnya.**
