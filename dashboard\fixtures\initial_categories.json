[{"model": "dashboard.category", "pk": 1, "fields": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> makanan dan bahan pangan"}}, {"model": "dashboard.category", "pk": 2, "fields": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> minuman dan bahan minuman"}}, {"model": "dashboard.category", "pk": 3, "fields": {"name": "Barang", "description": "Produk non-makanan dan non-minuman"}}]