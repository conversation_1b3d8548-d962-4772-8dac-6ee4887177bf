<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Notifikasi Stok Rendah</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .product-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .product-table th, .product-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .product-table th {
            background-color: #f2f2f2;
        }
        .warning {
            color: #856404;
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .danger {
            color: #721c24;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Notifikasi Stok Rendah</h2>
        </div>
        
        <p>Halo,</p>
        
        <p>Sistem Stock Manager mendeteksi beberapa produk dengan stok rendah atau habis yang memerlukan perhatian Anda:</p>
        
        {% if low_stock_products %}
        <div class="warning">
            <strong>Produk dengan Stok Rendah:</strong> {{ low_stock_products|length }} produk
        </div>
        
        <table class="product-table">
            <thead>
                <tr>
                    <th>Kode</th>
                    <th>Nama Produk</th>
                    <th>Stok Saat Ini</th>
                    <th>Stok Minimum</th>
                </tr>
            </thead>
            <tbody>
                {% for product in low_stock_products %}
                <tr>
                    <td>{{ product.code }}</td>
                    <td>{{ product.name }}</td>
                    <td>{{ product.quantity }}</td>
                    <td>{{ product.min_stock_level }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
        
        {% if out_of_stock_products %}
        <div class="danger">
            <strong>Produk dengan Stok Habis:</strong> {{ out_of_stock_products|length }} produk
        </div>
        
        <table class="product-table">
            <thead>
                <tr>
                    <th>Kode</th>
                    <th>Nama Produk</th>
                    <th>Stok Minimum</th>
                </tr>
            </thead>
            <tbody>
                {% for product in out_of_stock_products %}
                <tr>
                    <td>{{ product.code }}</td>
                    <td>{{ product.name }}</td>
                    <td>{{ product.min_stock_level }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
        
        <p>Mohon segera lakukan restock untuk produk-produk tersebut.</p>
        
        <p>Terima kasih,<br>
        Stock Manager System</p>
        
        <div class="footer">
            <p>Email ini dikirim secara otomatis oleh sistem. Mohon jangan membalas email ini.</p>
        </div>
    </div>
</body>
</html>