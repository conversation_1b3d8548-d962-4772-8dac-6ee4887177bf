# Contoh Penggunaan Sistem Notifikasi Email Stok Rendah

## 1. Setup Awal

### Pastikan Admin/Owner Me<PERSON><PERSON>i Email
```python
# Di Django shell atau admin panel
from django.contrib.auth.models import User

# Cek user admin/owner
admin_users = User.objects.filter(is_staff=True)
owner_users = User.objects.filter(is_superuser=True)

# Update email jika belum ada
user = User.objects.get(username='admin')
user.email = '<EMAIL>'
user.save()
```

### Cek Konfigurasi Email
```bash
# Test apakah email admin/owner sudah terdaftar
python manage.py test_email_notification --check-emails
```

Output yang diharapkan:
```
Admin users (1):
  - admin: <EMAIL> (AKTIF)

Owner users (1):
  - owner: <EMAIL> (AKTIF)

Email yang akan menerima notifikasi (2):
  - <EMAIL>
  - <EMAIL>
```

## 2. Testing Sistem Email

### Test Kirim Email
```bash
# Kirim email test
python manage.py test_email_notification --send-test
```

Jika menggunakan console backend, email akan muncul di terminal:
```
Content-Type: text/plain; charset="utf-8"
MIME-Version: 1.0
Content-Transfer-Encoding: 7bit
Subject: [Stock Manager] Stok Rendah - Nama Produk
From: <EMAIL>
To: <EMAIL>, <EMAIL>
Date: ...

Halo,

Sistem Stock Manager mendeteksi beberapa produk dengan stok rendah...
```

## 3. Skenario Penggunaan Normal

### Skenario 1: Stok Produk Menjadi Rendah
```python
# Ketika ada stock movement yang membuat stok rendah
from dashboard.models import Product, StockMovement

# Ambil produk
product = Product.objects.get(id=1)
print(f"Stok sebelum: {product.quantity}")
print(f"Min stock: {product.min_stock_level}")

# Buat stock movement OUT yang membuat stok rendah
movement = StockMovement.objects.create(
    product=product,
    quantity=10,  # Keluar 10 unit
    movement_type='OUT',
    notes='Penjualan'
)

# Sistem akan otomatis:
# 1. Update stok produk
# 2. Cek apakah stok rendah
# 3. Buat notifikasi
# 4. Kirim email ke admin/owner
```

### Skenario 2: Stok Produk Habis
```python
# Stock movement yang membuat stok habis
movement = StockMovement.objects.create(
    product=product,
    quantity=product.quantity,  # Keluar semua stok
    movement_type='OUT',
    notes='Habis terjual'
)

# Email "Stok Habis" akan dikirim otomatis
```

### Skenario 3: Restock Produk
```python
# Ketika stok masuk kembali
movement = StockMovement.objects.create(
    product=product,
    quantity=50,  # Masuk 50 unit
    movement_type='IN',
    notes='Restock dari supplier'
)

# Jika sebelumnya stok habis, notifikasi lama akan di-dismiss
# dan dibuat notifikasi "restock reminder"
```

## 4. Monitoring dan Maintenance

### Cek Stok Secara Manual
```bash
# Jalankan pengecekan stok manual
python manage.py check_stock
```

Output:
```
Memeriksa stok produk...
Pemeriksaan stok selesai. Ditemukan 3 produk stok rendah dan 1 produk stok habis.
```

### Kirim Email Batch
```bash
# Kirim email untuk semua produk stok rendah/habis saat ini
python manage.py send_stock_notifications --batch
```

### Kirim Email Individual
```bash
# Kirim email terpisah untuk setiap produk
python manage.py send_stock_notifications
```

## 5. Setup Cron Job (Production)

### Crontab untuk Pengecekan Otomatis
```bash
# Edit crontab
crontab -e

# Tambahkan baris berikut:
# Cek stok setiap jam dan kirim email jika ada yang baru
0 * * * * cd /path/to/stokmanager && python manage.py check_stock

# Kirim email batch setiap hari jam 9 pagi
0 9 * * * cd /path/to/stokmanager && python manage.py send_stock_notifications --batch

# Backup: cek dan kirim email setiap 6 jam
0 */6 * * * cd /path/to/stokmanager && python manage.py check_stock
```

## 6. Troubleshooting

### Problem: Email tidak terkirim
```bash
# 1. Cek email admin/owner
python manage.py test_email_notification --check-emails

# 2. Test kirim email
python manage.py test_email_notification --send-test

# 3. Cek log error di console
```

### Problem: Terlalu banyak email
```python
# Cek notifikasi yang sudah ada
from dashboard.models import Notification

# Lihat notifikasi yang belum di-dismiss
active_notifications = Notification.objects.filter(
    status__in=['unread', 'read']
)
print(f"Notifikasi aktif: {active_notifications.count()}")

# Dismiss notifikasi lama jika perlu
old_notifications = Notification.objects.filter(
    created_at__lt=timezone.now() - timedelta(days=7),
    status__in=['unread', 'read']
)
old_notifications.update(status='dismissed')
```

### Problem: Email masuk spam
1. Setup SPF record untuk domain
2. Gunakan DKIM signing
3. Pastikan FROM email valid
4. Jangan kirim terlalu sering

## 7. Customization

### Ubah Template Email
Edit file: `dashboard/templates/dashboard/emails/low_stock_notification.html`

### Ubah Logika Notifikasi
Edit fungsi di: `dashboard/utils.py`

### Tambah Penerima Email Lain
```python
# Modifikasi fungsi get_admin_and_owner_emails() di utils.py
def get_admin_and_owner_emails():
    # ... kode existing ...
    
    # Tambah email khusus
    emails.extend(['<EMAIL>', '<EMAIL>'])
    
    return emails
```

## 8. Monitoring Performa

### Log Email yang Dikirim
```python
# Tambah logging di utils.py
import logging

logger = logging.getLogger(__name__)

def send_low_stock_email_notification(product=None):
    # ... kode existing ...
    
    try:
        send_mail(...)
        logger.info(f"Email sent to {len(recipient_emails)} recipients for product {product.name if product else 'batch'}")
    except Exception as e:
        logger.error(f"Failed to send email: {e}")
```

### Database Query Optimization
```python
# Untuk performa yang lebih baik, gunakan select_related
low_stock_products = Product.objects.select_related('category', 'unit').filter(
    quantity__lte=F('min_stock_level'), 
    quantity__gt=0
)
```
