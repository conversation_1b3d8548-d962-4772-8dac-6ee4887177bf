from django.core.management.base import BaseCommand
from dashboard.models import Category

class Command(BaseCommand):
    help = 'Membuat kategori default (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ks<PERSON><PERSON>)'

    def handle(self, *args, **options):
        categories = [
            {'name': 'Tas', 'description': '<PERSON><PERSON><PERSON><PERSON> jenis tas'},
            {'name': '<PERSON><PERSON>', 'description': '<PERSON><PERSON><PERSON><PERSON> jenis paka<PERSON>'},
            {'name': 'Aks<PERSON><PERSON>', 'description': '<PERSON>rbagai jenis aksesoris'},
        ]
        
        created_count = 0
        for category_data in categories:
            category, created = Category.objects.get_or_create(
                name=category_data['name'],
                defaults={'description': category_data['description']}
            )
            if created:
                created_count += 1
                self.stdout.write(f'Kategori "{category.name}" berhasil dibuat.')
            else:
                self.stdout.write(f'Kategori "{category.name}" sudah ada.')
        
        self.stdout.write(self.style.SUCCESS(f'{created_count} kategori berhasil dibuat.'))
