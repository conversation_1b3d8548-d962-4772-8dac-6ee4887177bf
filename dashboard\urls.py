from django.urls import path
from . import views
from . import views_export

app_name = 'dashboard'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='index'),
    
    # Authentication
    path('login/', views.user_login, name='login'),
    path('logout/', views.user_logout, name='logout'),
    
    # Products
    path('products/', views.product_list, name='product_list'),
    path('products/create/', views.product_create, name='product_create'),
    path('products/<int:pk>/', views.product_detail, name='product_detail'),
    path('products/<int:pk>/edit/', views.product_edit, name='product_edit'),
    path('products/<int:pk>/delete/', views.product_delete, name='product_delete'),
    
    # Categories
    path('categories/', views.category_list, name='category_list'),
    path('categories/create/', views.category_create, name='category_create'),
    path('categories/<int:pk>/update/', views.category_update, name='category_update'),
    path('categories/<int:pk>/delete/', views.category_delete, name='category_delete'),
    
    # Units of Measure
    path('uom/', views.uom_list, name='uom_list'),
    path('uom/create/', views.uom_create, name='uom_create'),
    path('uom/<int:pk>/update/', views.uom_update, name='uom_update'),
    path('uom/<int:pk>/delete/', views.uom_delete, name='uom_delete'),
    
    # Stock Movements
    path('stock-in/', views.stock_in, name='stock_in'),
    path('stock-out/', views.stock_out, name='stock_out'),
    path('stock-movement/', views.stock_movement_list, name='stock_movement_list'),
    path('stock-movement/', views.stock_movement_list, name='stock_movement'),
    
    # Reports
    path('reports/stock/', views.stock_report, name='stock_report'),
    path('reports/stock/export/pdf/', views_export.export_stock_report_pdf, name='stock_report_export_pdf'),
    path('reports/stock/export/excel/', views_export.export_stock_report_excel, name='stock_report_export_excel'),
    path('reports/transaction/', views.transaction_report, name='transaction_report'),
    path('reports/financial/', views.financial_report, name='financial_report'),
    
    # Settings
    path('settings/', views.settings, name='settings'),
    
    # Notifications
    path('notifications/', views.notification_list, name='notification_list'),
    path('notifications/mark-read/<int:pk>/', views.mark_notification_as_read, name='notification_mark_read'),
    path('notifications/dismiss/<int:pk>/', views.dismiss_notification, name='notification_dismiss'),
    path('notifications/mark-all-read/', views.mark_all_as_read, name='notification_mark_all_read'),
    path('notifications/dismiss-all/', views.dismiss_all_notifications, name='notification_dismiss_all'),
    # URL untuk testing (hanya untuk development)
    path('notifications/create-test/', views.create_test_notifications, name='create_test_notifications'),
    
    # User Management
    path('users/', views.user_list, name='user_list'),
    path('users/create/', views.user_create, name='user_create'),
    path('users/<int:pk>/edit/', views.user_edit, name='user_edit'),
    path('users/<int:pk>/delete/', views.user_delete, name='user_delete'),
]














