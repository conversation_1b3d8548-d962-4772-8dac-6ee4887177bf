from django.db.models import F, Count
from .models import Product, Notification

class LowStockNotificationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Jalankan kode sebelum view diproses
        if request.user.is_authenticated:
            # Cek produk dengan stok rendah
            low_stock_count = Product.objects.filter(quantity__lte=F('min_stock_level'), quantity__gt=0).count()
            out_of_stock_count = Product.objects.filter(quantity=0).count()
            
            # Simpan jumlah notifikasi di request untuk diakses di template
            request.low_stock_count = low_stock_count
            request.out_of_stock_count = out_of_stock_count
            
            # Hitung notifikasi yang belum dibaca (hanya untuk sidebar, bukan dropdown)
            unread_notifications_count = Notification.objects.filter(status='unread').count()
            request.unread_notifications_count = unread_notifications_count
            
            # Hapus unread_notifications karena tidak lagi digunakan di dropdown
            # request.unread_notifications = unread_notifications.order_by('-created_at')[:5]
            
            # Hitung jumlah notifikasi berdasarkan tipe
            notification_counts = Notification.objects.filter(
                status__in=['unread', 'read']
            ).values('type').annotate(count=Count('id'))
            
            notification_counts_dict = {item['type']: item['count'] for item in notification_counts}
            
            request.low_stock_notification_count = notification_counts_dict.get('low_stock', 0)
            request.out_of_stock_notification_count = notification_counts_dict.get('out_of_stock', 0)
            request.restock_reminder_count = notification_counts_dict.get('restock_reminder', 0)
        
        response = self.get_response(request)
        
        # Jalankan kode setelah view diproses
        return response


