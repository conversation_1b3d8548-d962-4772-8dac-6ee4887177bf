from django.core.management.base import BaseCommand
from django.db.models import F
from dashboard.models import Product, Notification
from django.utils import timezone

class Command(BaseCommand):
    help = 'Memeriksa stok produk dan membuat notifikasi untuk stok rendah atau habis'

    def handle(self, *args, **options):
        self.stdout.write('Memeriksa stok produk...')
        
        # Cari produk dengan stok rendah
        low_stock_products = Product.objects.filter(quantity__lte=F('min_stock_level'), quantity__gt=0)
        
        low_stock_count = 0
        for product in low_stock_products:
            # Cek apakah sudah ada notifikasi yang belum dibaca untuk produk ini
            existing_notification = Notification.objects.filter(
                product=product,
                type='low_stock',
                status__in=['unread', 'read']
            ).exists()
            
            if not existing_notification:
                # Buat notifikasi baru
                Notification.objects.create(
                    product=product,
                    type='low_stock',
                    message=f"Stok produk {product.name} rendah. Sisa stok: {product.quantity}, minimum: {product.min_stock_level}."
                )
                low_stock_count += 1
        
        # Cari produk dengan stok habis
        out_of_stock_products = Product.objects.filter(quantity=0)
        
        out_of_stock_count = 0
        for product in out_of_stock_products:
            # Cek apakah sudah ada notifikasi yang belum dibaca untuk produk ini
            existing_notification = Notification.objects.filter(
                product=product,
                type='out_of_stock',
                status__in=['unread', 'read']
            ).exists()
            
            if not existing_notification:
                # Buat notifikasi baru
                Notification.objects.create(
                    product=product,
                    type='out_of_stock',
                    message=f"Stok produk {product.name} habis. Segera lakukan restock."
                )
                out_of_stock_count += 1
        
        self.stdout.write(self.style.SUCCESS(
            f'Pemeriksaan stok selesai. Ditemukan {low_stock_count} produk stok rendah dan {out_of_stock_count} produk stok habis.'
        ))