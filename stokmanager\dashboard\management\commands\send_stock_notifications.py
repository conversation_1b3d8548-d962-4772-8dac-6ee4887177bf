from django.core.management.base import BaseCommand
from dashboard.utils import send_low_stock_email_notification

class Command(BaseCommand):
    help = 'Mengirim email notifikasi untuk produk dengan stok rendah atau habis'

    def handle(self, *args, **options):
        self.stdout.write('Mengirim email notifikasi stok rendah...')
        
        send_low_stock_email_notification()
        
        self.stdout.write(self.style.SUCCESS('Email notifikasi berhasil dikirim.'))