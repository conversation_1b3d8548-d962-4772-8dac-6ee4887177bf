# Generated by Django 5.2.1 on 2025-05-28 03:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0002_category_unitofmeasure_product_code_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='expiry_date',
            field=models.DateField(blank=True, null=True, verbose_name='Tanggal Kedaluwarsa'),
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('low_stock', 'Stok Rendah'), ('out_of_stock', 'Stok Habis'), ('restock_reminder', 'Pengingat Restock')], max_length=20)),
                ('message', models.TextField()),
                ('status', models.CharField(choices=[('unread', 'Belum Dibaca'), ('read', 'Sudah Dibaca'), ('dismissed', 'Diabaikan')], default='unread', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='dashboard.product')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
