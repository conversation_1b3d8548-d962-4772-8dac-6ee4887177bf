{% extends "dashboard/base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block head_extras %}
<style>
    /* Form styling */
    .form-container {
        max-width: 900px;
        margin: 0 auto;
    }
    
    .form-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .form-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .form-header h4 {
        margin: 0;
        font-size: 20px;
        color: #333;
        font-weight: 600;
    }
    
    .form-body {
        padding: 25px;
    }
    
    .form-section {
        margin-bottom: 25px;
        padding-bottom: 25px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .form-section:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }
    
    .form-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .form-row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -10px;
        margin-left: -10px;
    }
    
    .form-group {
        padding-right: 10px;
        padding-left: 10px;
        margin-bottom: 20px;
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    @media (min-width: 768px) {
        .form-group.col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
        
        .form-group.col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }
        
        .form-group.col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }
    }
    
    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #555;
        font-size: 14px;
    }
    
    .form-control {
        display: block;
        width: 100%;
        padding: 10px 15px;
        font-size: 14px;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    textarea.form-control {
        height: auto;
        resize: vertical;
        min-height: 100px;
    }
    
    select.form-control {
        padding-right: 30px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='4' viewBox='0 0 8 4'%3E%3Cpath fill='%23343a40' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 8px 4px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }
    
    .form-text {
        display: block;
        margin-top: 5px;
        font-size: 12px;
        color: #6c757d;
    }
    
    .form-footer {
        padding: 20px;
        background-color: #f8f9fa;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 10px 20px;
        font-size: 14px;
        line-height: 1.5;
        border-radius: 4px;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        cursor: pointer;
    }
    
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .btn-primary:hover {
        color: #fff;
        background-color: #0069d9;
        border-color: #0062cc;
    }
    
    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }
    
    .btn-secondary:hover {
        color: #fff;
        background-color: #5a6268;
        border-color: #545b62;
    }
    
    .btn-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    .btn-icon i {
        margin-right: 8px;
    }
    
    .text-danger {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
    }
    
    /* Custom styling for specific form elements */
    .input-group {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        width: 100%;
    }
    
    .input-group-text {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        margin-bottom: 0;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        text-align: center;
        white-space: nowrap;
        background-color: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 4px 0 0 4px;
    }
    
    .input-group .form-control {
        position: relative;
        flex: 1 1 auto;
        width: 1%;
        margin-bottom: 0;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    /* Form validation styling */
    .was-validated .form-control:valid {
        border-color: #28a745;
        padding-right: calc(1.5em + 0.75rem);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
    
    .was-validated .form-control:invalid {
        border-color: #dc3545;
        padding-right: calc(1.5em + 0.75rem);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
    
    .valid-feedback {
        display: none;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 12px;
        color: #28a745;
    }
    
    .invalid-feedback {
        display: none;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 12px;
        color: #dc3545;
    }
    
    .was-validated .form-control:valid ~ .valid-feedback,
    .was-validated .form-control:invalid ~ .invalid-feedback {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="form-container">
        <div class="form-card">
            <div class="form-header">
                <h4>{{ title }}</h4>
            </div>
            <form method="post" class="needs-validation" novalidate>
                {% csrf_token %}
                
                <div class="form-body">
                    <!-- Informasi Dasar -->
                    <div class="form-section">
                        <h5 class="form-section-title">Informasi Dasar</h5>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="{{ form.code.id_for_label }}" class="form-label">Kode Barang / Barcode</label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                <div class="text-danger">{{ form.code.errors }}</div>
                                {% endif %}
                                <small class="form-text">Masukkan kode unik atau scan barcode produk</small>
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Nama Barang</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="text-danger">{{ form.name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="{{ form.category.id_for_label }}" class="form-label">Kategori</label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                <div class="text-danger">{{ form.category.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label for="{{ form.unit.id_for_label }}" class="form-label">Satuan</label>
                                {{ form.unit }}
                                {% if form.unit.errors %}
                                <div class="text-danger">{{ form.unit.errors }}</div>
                                {% endif %}
                                <small class="form-text">Contoh: Pcs, Box, Kg, Liter, dll</small>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="{{ form.size.id_for_label }}" class="form-label">Ukuran</label>
                                {{ form.size }}
                                {% if form.size.errors %}
                                <div class="text-danger">{{ form.size.errors }}</div>
                                {% endif %}
                                <small class="form-text">Pilih ukuran produk (jika ada)</small>
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label for="{{ form.color.id_for_label }}" class="form-label">Warna</label>
                                {{ form.color }}
                                {% if form.color.errors %}
                                <div class="text-danger">{{ form.color.errors }}</div>
                                {% endif %}
                                <small class="form-text">Pilih warna produk</small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Deskripsi</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                            <div class="text-danger">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Informasi Harga -->
                    <div class="form-section">
                        <h5 class="form-section-title">Informasi Harga</h5>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="{{ form.purchase_price.id_for_label }}" class="form-label">Harga Beli</label>
                                <div class="input-group">
                                    <div class="input-group-text">Rp</div>
                                    {{ form.purchase_price }}
                                </div>
                                {% if form.purchase_price.errors %}
                                <div class="text-danger">{{ form.purchase_price.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label for="{{ form.selling_price.id_for_label }}" class="form-label">Harga Jual</label>
                                <div class="input-group">
                                    <div class="input-group-text">Rp</div>
                                    {{ form.selling_price }}
                                </div>
                                {% if form.selling_price.errors %}
                                <div class="text-danger">{{ form.selling_price.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Informasi Stok -->
                    <div class="form-section">
                        <h5 class="form-section-title">Informasi Stok</h5>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="{{ form.quantity.id_for_label }}" class="form-label">Stok Awal</label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                <div class="text-danger">{{ form.quantity.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label for="{{ form.min_stock_level.id_for_label }}" class="form-label">Minimum Stok</label>
                                {{ form.min_stock_level }}
                                {% if form.min_stock_level.errors %}
                                <div class="text-danger">{{ form.min_stock_level.errors }}</div>
                                {% endif %}
                                <small class="form-text">Jumlah minimum stok sebelum notifikasi stok rendah muncul</small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.expiry_date.id_for_label }}" class="form-label">Tanggal Kedaluwarsa</label>
                            {{ form.expiry_date }}
                            {% if form.expiry_date.errors %}
                            <div class="text-danger">{{ form.expiry_date.errors }}</div>
                            {% endif %}
                            <small class="form-text">Kosongkan jika produk tidak memiliki tanggal kedaluwarsa</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-footer">
                    <a href="{% url 'dashboard:product_list' %}" class="btn btn-secondary btn-icon">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                    <button type="submit" class="btn btn-primary btn-icon">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Tambahkan class form-control ke semua input
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
        inputs.forEach(input => {
            input.classList.add('form-control');
        });
        
        // Tambahkan class form-check-input ke checkbox
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.classList.add('form-check-input');
        });
        
        // Form validation
        const form = document.querySelector('.needs-validation');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
</script>
{% endblock %}


