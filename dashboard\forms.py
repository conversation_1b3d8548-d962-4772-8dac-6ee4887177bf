from django import forms
from django.contrib.auth.models import User
from .models import Product, StockMovement, Category, UnitOfMeasure

class TailwindFormMixin:
    """Mixin untuk menerapkan kelas Tailwind CSS ke form fields"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            # Tambahkan kelas Tailwind ke semua field
            field.widget.attrs.update({
                'class': 'w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
            })
            
            # Tambahkan kelas khusus untuk checkbox
            if isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs.update({
                    'class': 'h-4 w-4 rounded border-input text-primary focus:ring-primary'
                })
            
            # Tambahkan kelas khusus untuk select
            if isinstance(field.widget, forms.Select):
                field.widget.attrs.update({
                    'class': 'w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
                })

class ProductSearchForm(TailwindFormMixin, forms.Form):
    search = forms.CharField(required=False, label="Cari", widget=forms.TextInput(attrs={'placeholder': 'Cari nama atau kode produk'}))
    category = forms.ModelChoiceField(queryset=Category.objects.exclude(name__in=['Makanan', 'Minuman', 'Barang']), required=False, empty_label="Semua Kategori")
    stock_status = forms.ChoiceField(choices=[
        ('', 'Semua Status'),
        ('normal', 'Stok Normal'),
        ('low', 'Stok Rendah'),
        ('out', 'Stok Habis')
    ], required=False)
    expiry_status = forms.ChoiceField(choices=[
        ('', 'Semua Status'),
        ('expiring', 'Akan Kedaluwarsa'),
        ('expired', 'Sudah Kedaluwarsa'),
        ('not_expired', 'Belum Kedaluwarsa')
    ], required=False)
    unit = forms.ModelChoiceField(queryset=UnitOfMeasure.objects.exclude(abbreviation__in=['KG', 'G', 'K', 'L', 'ml']), required=False, empty_label="Semua Satuan")

class ProductForm(TailwindFormMixin, forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter kategori: hilangkan makanan, minuman, barang
        self.fields['category'].queryset = Category.objects.exclude(name__in=['Makanan', 'Minuman', 'Barang'])
        # Filter satuan: hilangkan KG, G, ml (case-insensitive)
        self.fields['unit'].queryset = UnitOfMeasure.objects.exclude(abbreviation__in=['KG', 'kg', 'G', 'g', 'L', 'mL'])

    class Meta:
        model = Product
        fields = ['code', 'name', 'description', 'category', 'unit', 
                  'size', 'color', 'purchase_price', 'selling_price', 
                  'quantity', 'min_stock_level', 'expiry_date']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'expiry_date': forms.DateInput(attrs={'type': 'date'}),
        }

class StockMovementForm(TailwindFormMixin, forms.ModelForm):
    class Meta:
        model = StockMovement
        fields = ['product', 'quantity', 'movement_type', 'notes'
        ]
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

class UserForm(forms.ModelForm):
    ROLE_CHOICES = [
        ('user', 'User'),
        ('admin', 'Admin'),
        ('owner', 'Owner'),
    ]
    
    password = forms.CharField(widget=forms.PasswordInput(), required=False)
    confirm_password = forms.CharField(widget=forms.PasswordInput(), required=False)
    role = forms.ChoiceField(choices=ROLE_CHOICES, initial='user')
    
    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'first_name', 'last_name')
    
    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get('password')
        confirm_password = cleaned_data.get('confirm_password')
        
        if password and confirm_password:
            if password != confirm_password:
                raise forms.ValidationError("Password tidak cocok")
        
        return cleaned_data

# Tambahkan form untuk pencarian dan filter
class ProductSearchForm(forms.Form):
    search = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': 'Cari produk...'}))
    category = forms.ModelChoiceField(queryset=Category.objects.all(), required=False, empty_label="Semua Kategori")
    stock_status = forms.ChoiceField(choices=[
        ('', 'Semua Status'),
        ('low', 'Stok Rendah'),
        ('out', 'Stok Habis'),
        ('normal', 'Stok Normal'),
    ], required=False)
    expiry_status = forms.ChoiceField(choices=[
        ('', 'Semua Status'),
        ('expiring', 'Akan Kedaluwarsa'),
        ('expired', 'Sudah Kedaluwarsa'),
        ('not_expired', 'Belum Kedaluwarsa'),
    ], required=False)
    min_stock = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={'placeholder': 'Min'})
    )
    max_stock = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={'placeholder': 'Max'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Tambahkan class Bootstrap ke semua field
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.Select):
                field.widget.attrs['class'] = 'form-select'
            else:
                field.widget.attrs['class'] = 'form-control'

class StockMovementSearchForm(forms.Form):
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'placeholder': 'Cari produk'})
    )
    movement_type = forms.ChoiceField(
        choices=[
            ('', 'Semua Tipe'),
            ('IN', 'Stok Masuk'),
            ('OUT', 'Stok Keluar')
        ],
        required=False
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Tambahkan class Bootstrap ke semua field
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.Select):
                field.widget.attrs['class'] = 'form-select'
            else:
                field.widget.attrs['class'] = 'form-control'






