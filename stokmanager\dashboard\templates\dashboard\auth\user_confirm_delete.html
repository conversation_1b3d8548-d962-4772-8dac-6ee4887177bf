{% extends "dashboard/base.html" %}

{% block title %}Hapus Pengguna{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4>Konfirmasi Hapus</h4>
                </div>
                <div class="card-body">
                    <p class="lead">Apakah Anda yakin ingin menghapus pengguna <strong>{{ user_obj.username }}</strong>?</p>
                    <p>Tindakan ini tidak dapat dibatalkan.</p>
                    
                    <div class="alert alert-warning">
                        <strong>Informasi Pengguna:</strong>
                        <ul class="mb-0">
                            <li>Username: {{ user_obj.username }}</li>
                            <li>Nama: {{ user_obj.get_full_name|default:"Tidak ada" }}</li>
                            <li>Email: {{ user_obj.email|default:"Tidak ada" }}</li>
                            <li>Status: {% if user_obj.is_staff %}Admin{% else %}Pengguna{% endif %}</li>
                        </ul>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'dashboard:user_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Hapus Pengguna
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}